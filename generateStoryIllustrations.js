// 故事插画生成主脚本
// 为《小熊波波友谊冒险》生成完整的插画集

import openAIService from './openAIService.js';
import { buildConsistentPrompt, validateStyleConsistency } from './styleConsistencyManager.js';
import {
  batchProcessImages,
  generateProcessingReport,
  createBackup,
  fileExists,
  getImagePath
} from './imageProcessor.js';
// 故事数据 - 由于是TypeScript文件，我们直接定义数据
const storyData = {
  title: "小熊波波的友谊冒险",
  ageGroup: "6-8岁",
  theme: "友谊",
  pages: [
    {
      id: 1,
      content: "波波是一只住在森林里的小棕熊。他有一双好奇的大眼睛和一颗善良的心。每天早晨，波波都会坐在自己的小木屋前，望着远处的大树和花朵，但他从来不敢走得太远。",
      isInteractive: false
    },
    {
      id: 2,
      content: "一天早晨，波波听到一阵欢快的歌声。\"那是谁在唱歌呢？\"波波好奇地想。他鼓起勇气，第一次离开了自己的小木屋，沿着小路向歌声传来的方向走去。",
      isInteractive: false
    },
    {
      id: 3,
      content: "波波来到了一片开阔的草地。他看到一只小兔子正在那里采摘野花。小兔子有着雪白的毛发和粉红的鼻子，正哼着波波从未听过的歌曲。",
      isInteractive: false
    },
    {
      id: 5,
      content: "小兔子看到了波波，友好地笑了笑。\"你好，我叫莉莉！\"小兔子说，\"我正在采集这些美丽的花朵，准备做一个花环。你想一起来吗？\"波波点点头，慢慢地走近了莉莉。",
      isInteractive: false
    },
    {
      id: 6,
      content: "波波和莉莉一起采集花朵，他们边采边聊。莉莉告诉波波，森林里还有许多其他的动物朋友。\"我们每周五都会在大橡树下举行野餐会，\"莉莉说，\"你愿意来参加吗？\"",
      isInteractive: false
    },
    {
      id: 7,
      content: "波波既兴奋又紧张。他从来没有参加过野餐会，也没有见过那么多的动物朋友。但莉莉的笑容让他感到安心，于是他答应了。",
      isInteractive: false
    },
    {
      id: 9,
      content: "野餐会上，大家分享了各自带来的美食。猫头鹰带来了蜂蜜饼干，松鼠兄弟带来了坚果沙拉，乌龟带来了新鲜的浆果。波波没有带任何东西，他感到有些难过。",
      isInteractive: false
    },
    {
      id: 10,
      content: "\"别担心，波波，\"莉莉轻声说，\"重要的不是你带了什么，而是你来了。友谊不是用礼物来衡量的，而是用心来感受的。\"波波听了，心里暖暖的。",
      isInteractive: false
    },
    {
      id: 12,
      content: "现在，每天早晨，波波都会兴高采烈地离开自己的小木屋，去拜访他的朋友们。他知道，真正的友谊需要勇气去开始，需要时间去培养，更需要真心去维护。在森林里，波波找到了属于自己的幸福。",
      isInteractive: false
    }
  ]
};

/**
 * 需要生成插画的页面ID列表（非交互页面）
 */
const NON_INTERACTIVE_PAGES = [1, 2, 3, 5, 6, 7, 9, 10, 12];

/**
 * 生成单个页面的插画
 * @param {number} pageId - 页面ID
 * @param {Object} pageData - 页面数据
 * @returns {Promise<Object>} 生成结果
 */
async function generatePageIllustration(pageId, pageData) {
  try {
    console.log(`开始生成第${pageId}页插画...`);

    // 构建一致性提示词
    const prompt = buildConsistentPrompt(pageId, pageData.content);

    console.log(`提示词: ${prompt.substring(0, 200)}...`);

    // 调用OpenAI API生成图片
    const imageUrl = await openAIService.generateImage(prompt, "6-8岁");

    console.log(`第${pageId}页插画生成成功: ${imageUrl}`);

    // 验证风格一致性
    const validationResult = await validateStyleConsistency(imageUrl, pageId);

    return {
      pageId,
      imageUrl,
      prompt,
      validation: validationResult,
      success: true,
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    console.error(`第${pageId}页插画生成失败:`, error);
    return {
      pageId,
      error: error.message,
      success: false,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * 生成所有非交互页面的插画
 * @returns {Promise<Array>} 生成结果数组
 */
async function generateAllIllustrations() {
  console.log('开始生成故事插画集...');
  console.log(`需要生成插画的页面: ${NON_INTERACTIVE_PAGES.join(', ')}`);

  const results = [];

  for (const pageId of NON_INTERACTIVE_PAGES) {
    try {
      // 查找对应的页面数据
      const pageData = storyData.pages.find(page => page.id === pageId);

      if (!pageData) {
        console.error(`未找到页面${pageId}的数据`);
        results.push({
          pageId,
          error: '页面数据不存在',
          success: false
        });
        continue;
      }

      // 检查是否已存在图片
      const existingImagePath = `./public/page${pageId}.png`;
      if (fileExists(existingImagePath)) {
        console.log(`第${pageId}页插画已存在，创建备份...`);
        await createBackup(existingImagePath);
      }

      // 生成插画
      const result = await generatePageIllustration(pageId, pageData);
      results.push(result);

      // 添加延迟以避免API限制
      if (result.success) {
        console.log(`等待2秒后继续下一页...`);
        await new Promise(resolve => setTimeout(resolve, 2000));
      }

    } catch (error) {
      console.error(`处理页面${pageId}时发生错误:`, error);
      results.push({
        pageId,
        error: error.message,
        success: false
      });
    }
  }

  return results;
}

/**
 * 下载并保存生成的插画
 * @param {Array} generationResults - 生成结果数组
 * @returns {Promise<Array>} 下载结果数组
 */
async function downloadGeneratedImages(generationResults) {
  console.log('开始下载生成的插画...');

  const successfulResults = generationResults.filter(result => result.success && result.imageUrl);

  if (successfulResults.length === 0) {
    console.log('没有成功生成的插画需要下载');
    return [];
  }

  const imageData = successfulResults.map(result => ({
    url: result.imageUrl,
    pageId: result.pageId
  }));

  const downloadResults = await batchProcessImages(imageData, './public');

  return downloadResults;
}

/**
 * 更新故事数据中的图片路径
 * @param {Array} downloadResults - 下载结果数组
 * @returns {Object} 更新后的故事数据
 */
function updateStoryDataWithImages(downloadResults) {
  console.log('更新故事数据中的图片路径...');

  const updatedStoryData = { ...storyData };

  downloadResults.forEach(result => {
    if (result.success) {
      const pageIndex = updatedStoryData.pages.findIndex(page => page.id === result.pageId);
      if (pageIndex !== -1) {
        updatedStoryData.pages[pageIndex].image = result.imagePath;
        console.log(`更新第${result.pageId}页图片路径: ${result.imagePath}`);
      }
    }
  });

  return updatedStoryData;
}

/**
 * 生成完整报告
 * @param {Array} generationResults - 生成结果
 * @param {Array} downloadResults - 下载结果
 * @returns {Object} 完整报告
 */
function generateCompleteReport(generationResults, downloadResults) {
  const generationReport = generateProcessingReport(generationResults);
  const downloadReport = generateProcessingReport(downloadResults);

  return {
    generation: generationReport,
    download: downloadReport,
    summary: {
      totalPages: NON_INTERACTIVE_PAGES.length,
      generatedSuccessfully: generationResults.filter(r => r.success).length,
      downloadedSuccessfully: downloadResults.filter(r => r.success).length,
      overallSuccess: downloadResults.filter(r => r.success).length === NON_INTERACTIVE_PAGES.length
    },
    timestamp: new Date().toISOString()
  };
}

/**
 * 主函数：执行完整的插画生成流程
 * @param {string} apiKey - OpenAI API密钥
 * @returns {Promise<Object>} 执行结果
 */
export async function generateCompleteIllustrationSet(apiKey) {
  try {
    console.log('=== 开始生成《小熊波波友谊冒险》插画集 ===');

    // 初始化API密钥
    if (!openAIService.isApiKeyInitialized()) {
      if (!apiKey) {
        throw new Error('请提供OpenAI API密钥');
      }
      openAIService.initializeApiKey(apiKey);
      console.log('API密钥已初始化');
    }

    // 第一步：生成所有插画
    console.log('\n--- 第一步：生成插画 ---');
    const generationResults = await generateAllIllustrations();

    // 第二步：下载生成的插画
    console.log('\n--- 第二步：下载插画 ---');
    const downloadResults = await downloadGeneratedImages(generationResults);

    // 第三步：更新故事数据
    console.log('\n--- 第三步：更新故事数据 ---');
    const updatedStoryData = updateStoryDataWithImages(downloadResults);

    // 第四步：生成报告
    console.log('\n--- 第四步：生成报告 ---');
    const report = generateCompleteReport(generationResults, downloadResults);

    console.log('\n=== 插画生成完成 ===');
    console.log(`成功生成: ${report.summary.generatedSuccessfully}/${report.summary.totalPages} 页`);
    console.log(`成功下载: ${report.summary.downloadedSuccessfully}/${report.summary.totalPages} 页`);
    console.log(`整体成功率: ${report.summary.overallSuccess ? '100%' : '部分成功'}`);

    return {
      success: true,
      generationResults,
      downloadResults,
      updatedStoryData,
      report
    };

  } catch (error) {
    console.error('插画生成流程失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 重新生成指定页面的插画
 * @param {number} pageId - 页面ID
 * @param {string} apiKey - OpenAI API密钥
 * @returns {Promise<Object>} 生成结果
 */
export async function regeneratePageIllustration(pageId, apiKey) {
  try {
    console.log(`重新生成第${pageId}页插画...`);

    if (!openAIService.isApiKeyInitialized()) {
      if (!apiKey) {
        throw new Error('请提供OpenAI API密钥');
      }
      openAIService.initializeApiKey(apiKey);
    }

    const pageData = storyData.pages.find(page => page.id === pageId);
    if (!pageData) {
      throw new Error(`未找到页面${pageId}的数据`);
    }

    const result = await generatePageIllustration(pageId, pageData);

    if (result.success) {
      const downloadResult = await batchProcessImages([{
        url: result.imageUrl,
        pageId: pageId
      }], './public');

      return {
        success: true,
        generation: result,
        download: downloadResult[0]
      };
    }

    return result;

  } catch (error) {
    console.error(`重新生成第${pageId}页插画失败:`, error);
    return {
      success: false,
      error: error.message
    };
  }
}

export default {
  generateCompleteIllustrationSet,
  regeneratePageIllustration,
  NON_INTERACTIVE_PAGES
};
