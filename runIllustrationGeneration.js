// 执行插画生成的脚本
// 使用方法: node runIllustrationGeneration.js

import { generateCompleteIllustrationSet } from './generateStoryIllustrations.js';
import fs from 'fs';
import path from 'path';

/**
 * 从环境变量或用户输入获取API密钥
 */
function getApiKey() {
  // 首先尝试从环境变量获取
  if (process.env.OPENAI_API_KEY) {
    return process.env.OPENAI_API_KEY;
  }
  
  // 如果没有环境变量，提示用户手动输入
  console.log('请设置OPENAI_API_KEY环境变量，或者在代码中直接提供API密钥');
  console.log('例如: export OPENAI_API_KEY="your-api-key-here"');
  
  // 在实际使用时，可以在这里直接返回API密钥
  // return "your-api-key-here";
  
  return null;
}

/**
 * 保存生成报告到文件
 */
function saveReport(report, filename = 'illustration_generation_report.json') {
  try {
    const reportPath = path.join(process.cwd(), filename);
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2), 'utf8');
    console.log(`报告已保存到: ${reportPath}`);
  } catch (error) {
    console.error('保存报告失败:', error);
  }
}

/**
 * 保存更新后的故事数据
 */
function saveUpdatedStoryData(storyData, filename = 'updated_storyData.json') {
  try {
    const dataPath = path.join(process.cwd(), filename);
    fs.writeFileSync(dataPath, JSON.stringify(storyData, null, 2), 'utf8');
    console.log(`更新后的故事数据已保存到: ${dataPath}`);
  } catch (error) {
    console.error('保存故事数据失败:', error);
  }
}

/**
 * 主执行函数
 */
async function main() {
  console.log('=== 《小熊波波友谊冒险》插画生成工具 ===\n');
  
  try {
    // 获取API密钥
    const apiKey = getApiKey();
    
    if (!apiKey) {
      console.error('错误: 未提供OpenAI API密钥');
      console.log('\n请通过以下方式之一提供API密钥:');
      console.log('1. 设置环境变量: export OPENAI_API_KEY="your-key"');
      console.log('2. 在runIllustrationGeneration.js中直接设置');
      process.exit(1);
    }
    
    console.log('API密钥已获取，开始生成插画...\n');
    
    // 执行插画生成
    const result = await generateCompleteIllustrationSet(apiKey);
    
    if (result.success) {
      console.log('\n🎉 插画生成成功完成！');
      
      // 保存报告
      saveReport(result.report);
      
      // 保存更新后的故事数据
      saveUpdatedStoryData(result.updatedStoryData);
      
      // 显示摘要
      console.log('\n📊 生成摘要:');
      console.log(`- 总页数: ${result.report.summary.totalPages}`);
      console.log(`- 成功生成: ${result.report.summary.generatedSuccessfully}`);
      console.log(`- 成功下载: ${result.report.summary.downloadedSuccessfully}`);
      console.log(`- 整体成功: ${result.report.summary.overallSuccess ? '是' : '否'}`);
      
      if (result.report.download.failed > 0) {
        console.log('\n⚠️  部分页面处理失败:');
        result.report.download.failedPages.forEach(page => {
          console.log(`- 页面${page.pageId}: ${page.error}`);
        });
      }
      
    } else {
      console.error('\n❌ 插画生成失败:', result.error);
      process.exit(1);
    }
    
  } catch (error) {
    console.error('\n💥 执行过程中发生错误:', error);
    process.exit(1);
  }
}

/**
 * 处理命令行参数
 */
function handleCommandLineArgs() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log('《小熊波波友谊冒险》插画生成工具');
    console.log('\n使用方法:');
    console.log('  node runIllustrationGeneration.js');
    console.log('\n环境变量:');
    console.log('  OPENAI_API_KEY - OpenAI API密钥');
    console.log('\n选项:');
    console.log('  --help, -h     显示帮助信息');
    console.log('  --version, -v  显示版本信息');
    process.exit(0);
  }
  
  if (args.includes('--version') || args.includes('-v')) {
    console.log('版本: 1.0.0');
    process.exit(0);
  }
}

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
  process.exit(1);
});

// 主程序入口
if (import.meta.url === `file://${process.argv[1]}`) {
  handleCommandLineArgs();
  main();
}

export { main, getApiKey, saveReport, saveUpdatedStoryData };
