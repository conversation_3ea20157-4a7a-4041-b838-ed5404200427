// 测试插画生成功能
// 生成单个页面的插画进行测试

import { buildConsistentPrompt } from './styleConsistencyManager.js';
import { downloadImage, generateFilename } from './imageProcessor.js';

/**
 * 测试用的故事页面数据
 */
const testPageData = {
  id: 1,
  content: "波波是一只住在森林里的小棕熊。他有一双好奇的大眼睛和一颗善良的心。每天早晨，波波都会坐在自己的小木屋前，望着远处的大树和花朵，但他从来不敢走得太远。"
};

/**
 * 调用OpenAI API生成图片
 */
async function generateTestImage(prompt, apiKey) {
  try {
    const response = await fetch('https://api.openai.com/v1/images/generations', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: "dall-e-3",
        prompt: prompt,
        n: 1,
        size: "1024x1024",
        quality: "standard",
        style: "natural"
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`API错误: ${errorData.error?.message || '未知错误'}`);
    }

    const data = await response.json();
    return data.data[0].url;
  } catch (error) {
    console.error('图像生成失败:', error);
    throw error;
  }
}

/**
 * 测试单个页面插画生成
 */
async function testSinglePageGeneration(apiKey) {
  try {
    console.log('=== 测试单个页面插画生成 ===\n');
    
    // 构建提示词
    console.log('1. 构建提示词...');
    const prompt = buildConsistentPrompt(testPageData.id, testPageData.content);
    console.log('提示词长度:', prompt.length);
    console.log('提示词预览:', prompt.substring(0, 200) + '...\n');
    
    // 生成图片
    console.log('2. 调用DALL-E 3生成图片...');
    const imageUrl = await generateTestImage(prompt, apiKey);
    console.log('生成的图片URL:', imageUrl, '\n');
    
    // 下载图片
    console.log('3. 下载图片到本地...');
    const filename = generateFilename(testPageData.id, 'test_page');
    const localPath = await downloadImage(imageUrl, filename, './public');
    console.log('图片已保存到:', localPath, '\n');
    
    console.log('✅ 测试成功完成！');
    
    return {
      success: true,
      imageUrl,
      localPath,
      prompt
    };
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 验证API密钥
 */
async function validateApiKey(apiKey) {
  try {
    console.log('验证API密钥...');
    
    const response = await fetch('https://api.openai.com/v1/models', {
      headers: {
        'Authorization': `Bearer ${apiKey}`
      }
    });
    
    if (response.ok) {
      console.log('✅ API密钥有效');
      return true;
    } else {
      console.log('❌ API密钥无效');
      return false;
    }
  } catch (error) {
    console.error('验证API密钥时发生错误:', error);
    return false;
  }
}

/**
 * 主测试函数
 */
async function runTest() {
  console.log('=== 插画生成功能测试 ===\n');
  
  // 获取API密钥
  const apiKey = process.env.OPENAI_API_KEY;
  
  if (!apiKey) {
    console.error('错误: 请设置OPENAI_API_KEY环境变量');
    console.log('使用方法: export OPENAI_API_KEY="your-api-key-here"');
    process.exit(1);
  }
  
  try {
    // 验证API密钥
    const isValidKey = await validateApiKey(apiKey);
    if (!isValidKey) {
      console.error('API密钥验证失败，请检查密钥是否正确');
      process.exit(1);
    }
    
    // 执行测试
    const result = await testSinglePageGeneration(apiKey);
    
    if (result.success) {
      console.log('\n🎉 所有测试通过！可以继续进行完整的插画生成。');
      console.log('\n下一步: 运行 node runIllustrationGeneration.js 生成完整插画集');
    } else {
      console.log('\n❌ 测试失败，请检查错误信息并修复问题');
    }
    
  } catch (error) {
    console.error('测试过程中发生错误:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  runTest();
}

export { testSinglePageGeneration, validateApiKey };
