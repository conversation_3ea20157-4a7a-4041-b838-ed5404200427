// 风格一致性管理器
// 确保所有生成的插画保持统一的视觉风格

/**
 * 故事场景描述映射
 * 为每个故事页面定义详细的场景描述
 */
const SCENE_DESCRIPTIONS = {
  1: "小熊波波坐在森林中的小木屋前，望着远处的大树和花朵。波波有着棕色的毛发，圆圆的脸，大大的眼睛，表情好奇而温和。背景是茂密的绿色森林，阳光透过树叶洒下斑驳的光影。",
  
  2: "小熊波波鼓起勇气离开小木屋，沿着森林小路向前走。波波的表情显得既好奇又有些紧张，耳朵竖起在倾听远处的歌声。小路两旁是绿色的草丛和野花，阳光明媚。",
  
  3: "在一片开阔的草地上，小兔莉莉正在采摘五颜六色的野花。莉莉有着灰白色的毛发，长长的耳朵，粉红色的鼻子，正开心地哼着歌。草地上开满了各种颜色的野花，背景是蓝天白云。",
  
  5: "小熊波波和小兔莉莉一起在草地上采集花朵。两个小动物面对面站着，莉莉友好地向波波伸出爪子，波波的表情从紧张变为开心。他们周围散落着美丽的花朵，阳光温暖。",
  
  6: "波波和莉莉坐在草地上聊天，莉莉正在编织花环。两个小动物的表情都很开心，莉莉在向波波介绍森林里的其他朋友。背景是宁静的森林边缘，有大橡树的影子。",
  
  7: "小熊波波的表情既兴奋又紧张，莉莉在一旁鼓励他。波波的爪子紧张地搓在一起，但眼中闪烁着期待的光芒。背景是温暖的夕阳西下的森林。",
  
  9: "在大橡树下的野餐会场景。猫头鹰、松鼠兄弟、乌龟和莉莉围坐在一起，桌上摆满了各种美食：蜂蜜饼干、坚果沙拉、新鲜浆果。波波站在一旁，表情有些不安，但动物们都友善地看着他。",
  
  10: "小兔莉莉温柔地安慰着小熊波波。莉莉的表情温暖慈爱，波波的表情从难过转为感动。其他动物朋友们也围在周围，表情友善支持。背景是温暖的橡树下，光线柔和。",
  
  12: "小熊波波开心地和所有朋友们在一起。波波不再害羞，表情自信开朗，和朋友们一起玩耍。所有动物都表情愉快，背景是阳光明媚的森林，充满生机和活力。"
};

/**
 * 统一的风格参数
 */
const STYLE_PARAMETERS = {
  // 色彩方案
  colors: {
    primary: "温暖的棕色、森林绿色、天空蓝色",
    secondary: "阳光黄色、草地绿色、花朵粉色",
    accent: "柔和的紫色、橙色点缀"
  },
  
  // 艺术风格
  artStyle: "温暖友好的儿童插画，水彩画质感，轮廓线条清晰但柔和",
  
  // 构图要求
  composition: "简洁清晰的构图，主体突出，背景简洁不复杂",
  
  // 光线设置
  lighting: "柔和自然的光线，营造温馨舒适的氛围",
  
  // 情感基调
  mood: "温暖、友好、充满希望，传达友谊和成长的主题"
};

/**
 * 角色一致性描述
 */
const CHARACTER_CONSISTENCY = {
  波波: {
    appearance: "可爱的棕色小熊，圆脸，大眼睛，友好温暖的表情",
    clothing: "简单的红色背心",
    personality: "害羞但善良，逐渐变得勇敢和自信"
  },
  
  莉莉: {
    appearance: "灰白色的兔子，长耳朵，粉红色鼻子，温柔甜美的表情",
    clothing: "蓝色小裙子",
    personality: "友善、温暖、乐于助人"
  },
  
  猫头鹰: {
    appearance: "智慧的棕色猫头鹰，戴着小眼镜",
    clothing: "无特殊服装",
    personality: "聪明、严肃但友善"
  },
  
  松鼠兄弟: {
    appearance: "红棕色的松鼠，蓬松的尾巴，活泼好动的表情",
    clothing: "无特殊服装",
    personality: "活泼、好动、友善"
  },
  
  乌龟: {
    appearance: "绿色的乌龟，慢条斯理但友善的表情",
    clothing: "无特殊服装",
    personality: "智慧、耐心、友善"
  }
};

/**
 * 获取指定页面的场景描述
 * @param {number} pageId - 页面ID
 * @returns {string} 场景描述
 */
export function getSceneDescription(pageId) {
  return SCENE_DESCRIPTIONS[pageId] || "森林中的温馨场景，小动物们友好相处";
}

/**
 * 构建完整的插画生成提示词
 * @param {number} pageId - 页面ID
 * @param {string} additionalContext - 额外的上下文信息
 * @returns {string} 完整的提示词
 */
export function buildConsistentPrompt(pageId, additionalContext = "") {
  const sceneDescription = getSceneDescription(pageId);
  
  let prompt = `为儿童绘本《小熊波波友谊冒险》创作第${pageId}页的插图。

场景描述：${sceneDescription}`;

  if (additionalContext) {
    prompt += `\n\n额外情境：${additionalContext}`;
  }

  prompt += `

角色设定：`;
  
  Object.entries(CHARACTER_CONSISTENCY).forEach(([name, details]) => {
    prompt += `\n- ${name}：${details.appearance}${details.clothing ? `，${details.clothing}` : ''}`;
  });

  prompt += `

风格要求：
- ${STYLE_PARAMETERS.artStyle}
- 色彩方案：${STYLE_PARAMETERS.colors.primary}，${STYLE_PARAMETERS.colors.secondary}
- ${STYLE_PARAMETERS.composition}
- ${STYLE_PARAMETERS.lighting}
- 情感基调：${STYLE_PARAMETERS.mood}

技术要求：
- 高质量插画，适合儿童绘本
- 1024x1024像素，适合数字显示
- 主体角色突出，背景简洁
- 色彩饱和度适中，温暖舒适`;

  return prompt;
}

/**
 * 获取风格一致性检查参数
 * @returns {Object} 风格参数对象
 */
export function getStyleParameters() {
  return STYLE_PARAMETERS;
}

/**
 * 获取角色一致性描述
 * @param {string} characterName - 角色名称
 * @returns {Object} 角色描述对象
 */
export function getCharacterDescription(characterName) {
  return CHARACTER_CONSISTENCY[characterName] || null;
}

/**
 * 验证生成的插画是否符合风格要求
 * @param {string} imageUrl - 生成的图像URL
 * @param {number} pageId - 页面ID
 * @returns {Promise<Object>} 验证结果
 */
export async function validateStyleConsistency(imageUrl, pageId) {
  // 在实际应用中，这里可以实现图像分析逻辑
  // 比如检查色彩分布、构图等
  
  console.log(`验证第${pageId}页插画风格一致性:`, imageUrl);
  
  // 模拟验证过程
  return {
    isConsistent: true,
    confidence: 0.95,
    suggestions: []
  };
}

export default {
  getSceneDescription,
  buildConsistentPrompt,
  getStyleParameters,
  getCharacterDescription,
  validateStyleConsistency,
  SCENE_DESCRIPTIONS,
  STYLE_PARAMETERS,
  CHARACTER_CONSISTENCY
};
