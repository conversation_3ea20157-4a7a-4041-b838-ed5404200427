// 集成测试OpenAI服务的故事和图片生成功能

import openAIService from './services/openAIService';

// 初始化API密钥
const API_KEY = '********************************************************************************************************************************************************************';

// 测试故事生成功能
async function testStoryGeneration() {
  try {
    console.log('测试故事生成功能...');
    
    // 初始化API密钥
    openAIService.initializeApiKey(API_KEY);
    
    // 生成故事
    const story = await openAIService.generateStory('6-8岁', '友谊');
    console.log('生成的故事:', JSON.stringify(story, null, 2));
    
    return story;
  } catch (error) {
    console.error('测试故事生成失败:', error);
    throw error;
  }
}

// 测试图片生成功能
async function testImageGeneration() {
  try {
    console.log('测试图片生成功能...');
    
    // 确保API密钥已初始化
    if (!openAIService.isApiKeyInitialized()) {
      openAIService.initializeApiKey(API_KEY);
    }
    
    // 生成图片
    const sceneDescription = '一只可爱的小熊和一只兔子在森林里玩耍，周围有美丽的花朵和树木';
    const imageUrl = await openAIService.generateImage(sceneDescription, '6-8岁');
    console.log('生成的图片URL:', imageUrl);
    
    return imageUrl;
  } catch (error) {
    console.error('测试图片生成失败:', error);
    throw error;
  }
}

// 测试交互问题生成功能
async function testInteractiveQuestionGeneration() {
  try {
    console.log('测试交互问题生成功能...');
    
    // 确保API密钥已初始化
    if (!openAIService.isApiKeyInitialized()) {
      openAIService.initializeApiKey(API_KEY);
    }
    
    // 生成交互问题
    const storyContext = '小熊波波想和森林里的其他动物交朋友，但他很害羞，不知道该如何开始对话';
    const context = '初次见面打招呼';
    const question = await openAIService.generateInteractiveQuestion('6-8岁', '友谊', storyContext, context);
    console.log('生成的交互问题:', JSON.stringify(question, null, 2));
    
    return question;
  } catch (error) {
    console.error('测试交互问题生成失败:', error);
    throw error;
  }
}

// 测试性能分析功能
async function testPerformanceAnalysis() {
  try {
    console.log('测试性能分析功能...');
    
    // 确保API密钥已初始化
    if (!openAIService.isApiKeyInitialized()) {
      openAIService.initializeApiKey(API_KEY);
    }
    
    // 模拟问题和回答
    const questions = [
      '如果你是波波，你会怎么向小兔子打招呼呢？',
      '波波看到这么多新朋友有点害怕。如果你是波波，你会怎么融入这个新的朋友圈子？',
      '友谊给波波带来了哪些变化？你能想到一个你和朋友一起解决问题的经历吗？'
    ];
    
    const answers = [
      '你好，我叫波波，我喜欢你的歌声，我们可以做朋友吗？',
      '我会先和莉莉打招呼，因为她是我认识的朋友，然后请她帮我介绍其他朋友。',
      '友谊让波波变得更勇敢了，不再害怕和其他动物说话。我和我的朋友一起做过手工，我们合作完成了一个很大的纸箱城堡。'
    ];
    
    const analysis = await openAIService.analyzePerformance(questions, answers);
    console.log('性能分析结果:', JSON.stringify(analysis, null, 2));
    
    return analysis;
  } catch (error) {
    console.error('测试性能分析失败:', error);
    throw error;
  }
}

// 运行所有测试
async function runAllTests() {
  try {
    // 测试故事生成
    const story = await testStoryGeneration();
    
    // 测试图片生成
    const imageUrl = await testImageGeneration();
    
    // 测试交互问题生成
    const question = await testInteractiveQuestionGeneration();
    
    // 测试性能分析
    const analysis = await testPerformanceAnalysis();
    
    // 清理API密钥
    openAIService.clearApiKey();
    
    console.log('所有测试完成！');
    
    return {
      story,
      imageUrl,
      question,
      analysis
    };
  } catch (error) {
    console.error('测试过程中发生错误:', error);
    
    // 确保清理API密钥
    try {
      openAIService.clearApiKey();
    } catch (e) {
      console.error('清理API密钥失败:', e);
    }
    
    throw error;
  }
}

// 导出测试函数
export {
  testStoryGeneration,
  testImageGeneration,
  testInteractiveQuestionGeneration,
  testPerformanceAnalysis,
  runAllTests
};
