// 图片处理工具
// 处理生成的图片，包括下载、保存和格式转换

import fs from 'fs';
import path from 'path';
import https from 'https';
import http from 'http';

/**
 * 从URL下载图片到本地
 * @param {string} imageUrl - 图片URL
 * @param {string} filename - 保存的文件名
 * @param {string} directory - 保存目录
 * @returns {Promise<string>} 本地文件路径
 */
export async function downloadImage(imageUrl, filename, directory = './public') {
  return new Promise((resolve, reject) => {
    // 确保目录存在
    if (!fs.existsSync(directory)) {
      fs.mkdirSync(directory, { recursive: true });
    }

    const filePath = path.join(directory, filename);
    const file = fs.createWriteStream(filePath);

    // 选择合适的协议
    const client = imageUrl.startsWith('https:') ? https : http;

    const request = client.get(imageUrl, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`下载失败，状态码: ${response.statusCode}`));
        return;
      }

      response.pipe(file);

      file.on('finish', () => {
        file.close();
        console.log(`图片已保存: ${filePath}`);
        resolve(filePath);
      });

      file.on('error', (err) => {
        fs.unlink(filePath, () => {}); // 删除不完整的文件
        reject(err);
      });
    });

    request.on('error', (err) => {
      reject(err);
    });

    request.setTimeout(30000, () => {
      request.destroy();
      reject(new Error('下载超时'));
    });
  });
}

/**
 * 生成标准化的文件名
 * @param {number} pageId - 页面ID
 * @param {string} prefix - 文件名前缀
 * @param {string} extension - 文件扩展名
 * @returns {string} 标准化的文件名
 */
export function generateFilename(pageId, prefix = 'page', extension = 'png') {
  return `${prefix}${pageId}.${extension}`;
}

/**
 * 获取图片的相对路径（用于前端引用）
 * @param {string} filename - 文件名
 * @returns {string} 相对路径
 */
export function getImagePath(filename) {
  return `/${filename}`;
}

/**
 * 检查文件是否存在
 * @param {string} filePath - 文件路径
 * @returns {boolean} 文件是否存在
 */
export function fileExists(filePath) {
  try {
    return fs.existsSync(filePath);
  } catch (error) {
    return false;
  }
}

/**
 * 创建图片备份
 * @param {string} originalPath - 原始文件路径
 * @param {string} backupDirectory - 备份目录
 * @returns {Promise<string>} 备份文件路径
 */
export async function createBackup(originalPath, backupDirectory = './backup') {
  return new Promise((resolve, reject) => {
    if (!fs.existsSync(backupDirectory)) {
      fs.mkdirSync(backupDirectory, { recursive: true });
    }

    const filename = path.basename(originalPath);
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFilename = `${timestamp}_${filename}`;
    const backupPath = path.join(backupDirectory, backupFilename);

    fs.copyFile(originalPath, backupPath, (err) => {
      if (err) {
        reject(err);
      } else {
        console.log(`备份已创建: ${backupPath}`);
        resolve(backupPath);
      }
    });
  });
}

/**
 * 批量处理图片
 * @param {Array} imageData - 图片数据数组 [{url, pageId, ...}]
 * @param {string} directory - 保存目录
 * @returns {Promise<Array>} 处理结果数组
 */
export async function batchProcessImages(imageData, directory = './public') {
  const results = [];
  
  for (const item of imageData) {
    try {
      const filename = generateFilename(item.pageId);
      const localPath = await downloadImage(item.url, filename, directory);
      const imagePath = getImagePath(filename);
      
      results.push({
        pageId: item.pageId,
        originalUrl: item.url,
        localPath: localPath,
        imagePath: imagePath,
        success: true
      });
      
      // 添加延迟以避免API限制
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } catch (error) {
      console.error(`处理页面${item.pageId}的图片失败:`, error);
      results.push({
        pageId: item.pageId,
        originalUrl: item.url,
        error: error.message,
        success: false
      });
    }
  }
  
  return results;
}

/**
 * 清理临时文件
 * @param {Array} filePaths - 要清理的文件路径数组
 */
export function cleanupTempFiles(filePaths) {
  filePaths.forEach(filePath => {
    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        console.log(`临时文件已删除: ${filePath}`);
      }
    } catch (error) {
      console.error(`删除临时文件失败: ${filePath}`, error);
    }
  });
}

/**
 * 获取图片信息
 * @param {string} filePath - 图片文件路径
 * @returns {Object} 图片信息
 */
export function getImageInfo(filePath) {
  try {
    const stats = fs.statSync(filePath);
    return {
      exists: true,
      size: stats.size,
      created: stats.birthtime,
      modified: stats.mtime,
      path: filePath
    };
  } catch (error) {
    return {
      exists: false,
      error: error.message
    };
  }
}

/**
 * 验证图片文件
 * @param {string} filePath - 图片文件路径
 * @returns {boolean} 是否为有效的图片文件
 */
export function validateImageFile(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      return false;
    }
    
    const stats = fs.statSync(filePath);
    
    // 检查文件大小（至少1KB，最多10MB）
    if (stats.size < 1024 || stats.size > 10 * 1024 * 1024) {
      return false;
    }
    
    // 检查文件扩展名
    const ext = path.extname(filePath).toLowerCase();
    const validExtensions = ['.png', '.jpg', '.jpeg', '.gif', '.webp'];
    
    return validExtensions.includes(ext);
  } catch (error) {
    return false;
  }
}

/**
 * 生成图片处理报告
 * @param {Array} results - 处理结果数组
 * @returns {Object} 处理报告
 */
export function generateProcessingReport(results) {
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  return {
    total: results.length,
    successful: successful.length,
    failed: failed.length,
    successRate: (successful.length / results.length * 100).toFixed(2) + '%',
    successfulPages: successful.map(r => r.pageId),
    failedPages: failed.map(r => ({ pageId: r.pageId, error: r.error })),
    timestamp: new Date().toISOString()
  };
}

export default {
  downloadImage,
  generateFilename,
  getImagePath,
  fileExists,
  createBackup,
  batchProcessImages,
  cleanupTempFiles,
  getImageInfo,
  validateImageFile,
  generateProcessingReport
};
