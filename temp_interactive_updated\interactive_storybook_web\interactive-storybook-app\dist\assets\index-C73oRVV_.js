(function(){const d=document.createElement("link").relList;if(d&&d.supports&&d.supports("modulepreload"))return;for(const g of document.querySelectorAll('link[rel="modulepreload"]'))v(g);new MutationObserver(g=>{for(const x of g)if(x.type==="childList")for(const N of x.addedNodes)N.tagName==="LINK"&&N.rel==="modulepreload"&&v(N)}).observe(document,{childList:!0,subtree:!0});function u(g){const x={};return g.integrity&&(x.integrity=g.integrity),g.referrerPolicy&&(x.referrerPolicy=g.referrerPolicy),g.crossOrigin==="use-credentials"?x.credentials="include":g.crossOrigin==="anonymous"?x.credentials="omit":x.credentials="same-origin",x}function v(g){if(g.ep)return;g.ep=!0;const x=u(g);fetch(g.href,x)}})();var $i={exports:{}},Tr={},Wi={exports:{}},te={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qa;function Cf(){if(Qa)return te;Qa=1;var s=Symbol.for("react.element"),d=Symbol.for("react.portal"),u=Symbol.for("react.fragment"),v=Symbol.for("react.strict_mode"),g=Symbol.for("react.profiler"),x=Symbol.for("react.provider"),N=Symbol.for("react.context"),S=Symbol.for("react.forward_ref"),k=Symbol.for("react.suspense"),L=Symbol.for("react.memo"),W=Symbol.for("react.lazy"),A=Symbol.iterator;function D(p){return p===null||typeof p!="object"?null:(p=A&&p[A]||p["@@iterator"],typeof p=="function"?p:null)}var ne={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},q=Object.assign,B={};function I(p,C,G){this.props=p,this.context=C,this.refs=B,this.updater=G||ne}I.prototype.isReactComponent={},I.prototype.setState=function(p,C){if(typeof p!="object"&&typeof p!="function"&&p!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,p,C,"setState")},I.prototype.forceUpdate=function(p){this.updater.enqueueForceUpdate(this,p,"forceUpdate")};function Y(){}Y.prototype=I.prototype;function oe(p,C,G){this.props=p,this.context=C,this.refs=B,this.updater=G||ne}var Ce=oe.prototype=new Y;Ce.constructor=oe,q(Ce,I.prototype),Ce.isPureReactComponent=!0;var ue=Array.isArray,Ie=Object.prototype.hasOwnProperty,Ee={current:null},ze={key:!0,ref:!0,__self:!0,__source:!0};function Le(p,C,G){var Z,le={},ie=null,me=null;if(C!=null)for(Z in C.ref!==void 0&&(me=C.ref),C.key!==void 0&&(ie=""+C.key),C)Ie.call(C,Z)&&!ze.hasOwnProperty(Z)&&(le[Z]=C[Z]);var de=arguments.length-2;if(de===1)le.children=G;else if(1<de){for(var ge=Array(de),Ye=0;Ye<de;Ye++)ge[Ye]=arguments[Ye+2];le.children=ge}if(p&&p.defaultProps)for(Z in de=p.defaultProps,de)le[Z]===void 0&&(le[Z]=de[Z]);return{$$typeof:s,type:p,key:ie,ref:me,props:le,_owner:Ee.current}}function re(p,C){return{$$typeof:s,type:p.type,key:C,ref:p.ref,props:p.props,_owner:p._owner}}function ye(p){return typeof p=="object"&&p!==null&&p.$$typeof===s}function he(p){var C={"=":"=0",":":"=2"};return"$"+p.replace(/[=:]/g,function(G){return C[G]})}var X=/\/+/g;function Ae(p,C){return typeof p=="object"&&p!==null&&p.key!=null?he(""+p.key):C.toString(36)}function b(p,C,G,Z,le){var ie=typeof p;(ie==="undefined"||ie==="boolean")&&(p=null);var me=!1;if(p===null)me=!0;else switch(ie){case"string":case"number":me=!0;break;case"object":switch(p.$$typeof){case s:case d:me=!0}}if(me)return me=p,le=le(me),p=Z===""?"."+Ae(me,0):Z,ue(le)?(G="",p!=null&&(G=p.replace(X,"$&/")+"/"),b(le,C,G,"",function(Ye){return Ye})):le!=null&&(ye(le)&&(le=re(le,G+(!le.key||me&&me.key===le.key?"":(""+le.key).replace(X,"$&/")+"/")+p)),C.push(le)),1;if(me=0,Z=Z===""?".":Z+":",ue(p))for(var de=0;de<p.length;de++){ie=p[de];var ge=Z+Ae(ie,de);me+=b(ie,C,G,ge,le)}else if(ge=D(p),typeof ge=="function")for(p=ge.call(p),de=0;!(ie=p.next()).done;)ie=ie.value,ge=Z+Ae(ie,de++),me+=b(ie,C,G,ge,le);else if(ie==="object")throw C=String(p),Error("Objects are not valid as a React child (found: "+(C==="[object Object]"?"object with keys {"+Object.keys(p).join(", ")+"}":C)+"). If you meant to render a collection of children, use an array instead.");return me}function fe(p,C,G){if(p==null)return p;var Z=[],le=0;return b(p,Z,"","",function(ie){return C.call(G,ie,le++)}),Z}function ce(p){if(p._status===-1){var C=p._result;C=C(),C.then(function(G){(p._status===0||p._status===-1)&&(p._status=1,p._result=G)},function(G){(p._status===0||p._status===-1)&&(p._status=2,p._result=G)}),p._status===-1&&(p._status=0,p._result=C)}if(p._status===1)return p._result.default;throw p._result}var ee={current:null},P={transition:null},F={ReactCurrentDispatcher:ee,ReactCurrentBatchConfig:P,ReactCurrentOwner:Ee};function R(){throw Error("act(...) is not supported in production builds of React.")}return te.Children={map:fe,forEach:function(p,C,G){fe(p,function(){C.apply(this,arguments)},G)},count:function(p){var C=0;return fe(p,function(){C++}),C},toArray:function(p){return fe(p,function(C){return C})||[]},only:function(p){if(!ye(p))throw Error("React.Children.only expected to receive a single React element child.");return p}},te.Component=I,te.Fragment=u,te.Profiler=g,te.PureComponent=oe,te.StrictMode=v,te.Suspense=k,te.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=F,te.act=R,te.cloneElement=function(p,C,G){if(p==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+p+".");var Z=q({},p.props),le=p.key,ie=p.ref,me=p._owner;if(C!=null){if(C.ref!==void 0&&(ie=C.ref,me=Ee.current),C.key!==void 0&&(le=""+C.key),p.type&&p.type.defaultProps)var de=p.type.defaultProps;for(ge in C)Ie.call(C,ge)&&!ze.hasOwnProperty(ge)&&(Z[ge]=C[ge]===void 0&&de!==void 0?de[ge]:C[ge])}var ge=arguments.length-2;if(ge===1)Z.children=G;else if(1<ge){de=Array(ge);for(var Ye=0;Ye<ge;Ye++)de[Ye]=arguments[Ye+2];Z.children=de}return{$$typeof:s,type:p.type,key:le,ref:ie,props:Z,_owner:me}},te.createContext=function(p){return p={$$typeof:N,_currentValue:p,_currentValue2:p,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},p.Provider={$$typeof:x,_context:p},p.Consumer=p},te.createElement=Le,te.createFactory=function(p){var C=Le.bind(null,p);return C.type=p,C},te.createRef=function(){return{current:null}},te.forwardRef=function(p){return{$$typeof:S,render:p}},te.isValidElement=ye,te.lazy=function(p){return{$$typeof:W,_payload:{_status:-1,_result:p},_init:ce}},te.memo=function(p,C){return{$$typeof:L,type:p,compare:C===void 0?null:C}},te.startTransition=function(p){var C=P.transition;P.transition={};try{p()}finally{P.transition=C}},te.unstable_act=R,te.useCallback=function(p,C){return ee.current.useCallback(p,C)},te.useContext=function(p){return ee.current.useContext(p)},te.useDebugValue=function(){},te.useDeferredValue=function(p){return ee.current.useDeferredValue(p)},te.useEffect=function(p,C){return ee.current.useEffect(p,C)},te.useId=function(){return ee.current.useId()},te.useImperativeHandle=function(p,C,G){return ee.current.useImperativeHandle(p,C,G)},te.useInsertionEffect=function(p,C){return ee.current.useInsertionEffect(p,C)},te.useLayoutEffect=function(p,C){return ee.current.useLayoutEffect(p,C)},te.useMemo=function(p,C){return ee.current.useMemo(p,C)},te.useReducer=function(p,C,G){return ee.current.useReducer(p,C,G)},te.useRef=function(p){return ee.current.useRef(p)},te.useState=function(p){return ee.current.useState(p)},te.useSyncExternalStore=function(p,C,G){return ee.current.useSyncExternalStore(p,C,G)},te.useTransition=function(){return ee.current.useTransition()},te.version="18.3.1",te}var Ka;function Yi(){return Ka||(Ka=1,Wi.exports=Cf()),Wi.exports}/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ga;function Ef(){if(Ga)return Tr;Ga=1;var s=Yi(),d=Symbol.for("react.element"),u=Symbol.for("react.fragment"),v=Object.prototype.hasOwnProperty,g=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,x={key:!0,ref:!0,__self:!0,__source:!0};function N(S,k,L){var W,A={},D=null,ne=null;L!==void 0&&(D=""+L),k.key!==void 0&&(D=""+k.key),k.ref!==void 0&&(ne=k.ref);for(W in k)v.call(k,W)&&!x.hasOwnProperty(W)&&(A[W]=k[W]);if(S&&S.defaultProps)for(W in k=S.defaultProps,k)A[W]===void 0&&(A[W]=k[W]);return{$$typeof:d,type:S,key:D,ref:ne,props:A,_owner:g.current}}return Tr.Fragment=u,Tr.jsx=N,Tr.jsxs=N,Tr}var Ya;function _f(){return Ya||(Ya=1,$i.exports=Ef()),$i.exports}var w=_f(),$=Yi(),Hl={},Bi={exports:{}},et={},Hi={exports:{}},Qi={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Xa;function Nf(){return Xa||(Xa=1,function(s){function d(P,F){var R=P.length;P.push(F);e:for(;0<R;){var p=R-1>>>1,C=P[p];if(0<g(C,F))P[p]=F,P[R]=C,R=p;else break e}}function u(P){return P.length===0?null:P[0]}function v(P){if(P.length===0)return null;var F=P[0],R=P.pop();if(R!==F){P[0]=R;e:for(var p=0,C=P.length,G=C>>>1;p<G;){var Z=2*(p+1)-1,le=P[Z],ie=Z+1,me=P[ie];if(0>g(le,R))ie<C&&0>g(me,le)?(P[p]=me,P[ie]=R,p=ie):(P[p]=le,P[Z]=R,p=Z);else if(ie<C&&0>g(me,R))P[p]=me,P[ie]=R,p=ie;else break e}}return F}function g(P,F){var R=P.sortIndex-F.sortIndex;return R!==0?R:P.id-F.id}if(typeof performance=="object"&&typeof performance.now=="function"){var x=performance;s.unstable_now=function(){return x.now()}}else{var N=Date,S=N.now();s.unstable_now=function(){return N.now()-S}}var k=[],L=[],W=1,A=null,D=3,ne=!1,q=!1,B=!1,I=typeof setTimeout=="function"?setTimeout:null,Y=typeof clearTimeout=="function"?clearTimeout:null,oe=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function Ce(P){for(var F=u(L);F!==null;){if(F.callback===null)v(L);else if(F.startTime<=P)v(L),F.sortIndex=F.expirationTime,d(k,F);else break;F=u(L)}}function ue(P){if(B=!1,Ce(P),!q)if(u(k)!==null)q=!0,ce(Ie);else{var F=u(L);F!==null&&ee(ue,F.startTime-P)}}function Ie(P,F){q=!1,B&&(B=!1,Y(Le),Le=-1),ne=!0;var R=D;try{for(Ce(F),A=u(k);A!==null&&(!(A.expirationTime>F)||P&&!he());){var p=A.callback;if(typeof p=="function"){A.callback=null,D=A.priorityLevel;var C=p(A.expirationTime<=F);F=s.unstable_now(),typeof C=="function"?A.callback=C:A===u(k)&&v(k),Ce(F)}else v(k);A=u(k)}if(A!==null)var G=!0;else{var Z=u(L);Z!==null&&ee(ue,Z.startTime-F),G=!1}return G}finally{A=null,D=R,ne=!1}}var Ee=!1,ze=null,Le=-1,re=5,ye=-1;function he(){return!(s.unstable_now()-ye<re)}function X(){if(ze!==null){var P=s.unstable_now();ye=P;var F=!0;try{F=ze(!0,P)}finally{F?Ae():(Ee=!1,ze=null)}}else Ee=!1}var Ae;if(typeof oe=="function")Ae=function(){oe(X)};else if(typeof MessageChannel<"u"){var b=new MessageChannel,fe=b.port2;b.port1.onmessage=X,Ae=function(){fe.postMessage(null)}}else Ae=function(){I(X,0)};function ce(P){ze=P,Ee||(Ee=!0,Ae())}function ee(P,F){Le=I(function(){P(s.unstable_now())},F)}s.unstable_IdlePriority=5,s.unstable_ImmediatePriority=1,s.unstable_LowPriority=4,s.unstable_NormalPriority=3,s.unstable_Profiling=null,s.unstable_UserBlockingPriority=2,s.unstable_cancelCallback=function(P){P.callback=null},s.unstable_continueExecution=function(){q||ne||(q=!0,ce(Ie))},s.unstable_forceFrameRate=function(P){0>P||125<P?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):re=0<P?Math.floor(1e3/P):5},s.unstable_getCurrentPriorityLevel=function(){return D},s.unstable_getFirstCallbackNode=function(){return u(k)},s.unstable_next=function(P){switch(D){case 1:case 2:case 3:var F=3;break;default:F=D}var R=D;D=F;try{return P()}finally{D=R}},s.unstable_pauseExecution=function(){},s.unstable_requestPaint=function(){},s.unstable_runWithPriority=function(P,F){switch(P){case 1:case 2:case 3:case 4:case 5:break;default:P=3}var R=D;D=P;try{return F()}finally{D=R}},s.unstable_scheduleCallback=function(P,F,R){var p=s.unstable_now();switch(typeof R=="object"&&R!==null?(R=R.delay,R=typeof R=="number"&&0<R?p+R:p):R=p,P){case 1:var C=-1;break;case 2:C=250;break;case 5:C=**********;break;case 4:C=1e4;break;default:C=5e3}return C=R+C,P={id:W++,callback:F,priorityLevel:P,startTime:R,expirationTime:C,sortIndex:-1},R>p?(P.sortIndex=R,d(L,P),u(k)===null&&P===u(L)&&(B?(Y(Le),Le=-1):B=!0,ee(ue,R-p))):(P.sortIndex=C,d(k,P),q||ne||(q=!0,ce(Ie))),P},s.unstable_shouldYield=he,s.unstable_wrapCallback=function(P){var F=D;return function(){var R=D;D=F;try{return P.apply(this,arguments)}finally{D=R}}}}(Qi)),Qi}var Ja;function Pf(){return Ja||(Ja=1,Hi.exports=Nf()),Hi.exports}/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Za;function zf(){if(Za)return et;Za=1;var s=Yi(),d=Pf();function u(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var v=new Set,g={};function x(e,t){N(e,t),N(e+"Capture",t)}function N(e,t){for(g[e]=t,e=0;e<t.length;e++)v.add(t[e])}var S=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),k=Object.prototype.hasOwnProperty,L=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,W={},A={};function D(e){return k.call(A,e)?!0:k.call(W,e)?!1:L.test(e)?A[e]=!0:(W[e]=!0,!1)}function ne(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function q(e,t,n,r){if(t===null||typeof t>"u"||ne(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function B(e,t,n,r,l,o,i){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=l,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=i}var I={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){I[e]=new B(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];I[t]=new B(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){I[e]=new B(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){I[e]=new B(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){I[e]=new B(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){I[e]=new B(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){I[e]=new B(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){I[e]=new B(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){I[e]=new B(e,5,!1,e.toLowerCase(),null,!1,!1)});var Y=/[\-:]([a-z])/g;function oe(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Y,oe);I[t]=new B(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Y,oe);I[t]=new B(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Y,oe);I[t]=new B(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){I[e]=new B(e,1,!1,e.toLowerCase(),null,!1,!1)}),I.xlinkHref=new B("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){I[e]=new B(e,1,!1,e.toLowerCase(),null,!0,!0)});function Ce(e,t,n,r){var l=I.hasOwnProperty(t)?I[t]:null;(l!==null?l.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(q(t,n,l,r)&&(n=null),r||l===null?D(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):l.mustUseProperty?e[l.propertyName]=n===null?l.type===3?!1:"":n:(t=l.attributeName,r=l.attributeNamespace,n===null?e.removeAttribute(t):(l=l.type,n=l===3||l===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var ue=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Ie=Symbol.for("react.element"),Ee=Symbol.for("react.portal"),ze=Symbol.for("react.fragment"),Le=Symbol.for("react.strict_mode"),re=Symbol.for("react.profiler"),ye=Symbol.for("react.provider"),he=Symbol.for("react.context"),X=Symbol.for("react.forward_ref"),Ae=Symbol.for("react.suspense"),b=Symbol.for("react.suspense_list"),fe=Symbol.for("react.memo"),ce=Symbol.for("react.lazy"),ee=Symbol.for("react.offscreen"),P=Symbol.iterator;function F(e){return e===null||typeof e!="object"?null:(e=P&&e[P]||e["@@iterator"],typeof e=="function"?e:null)}var R=Object.assign,p;function C(e){if(p===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);p=t&&t[1]||""}return`
`+p+e}var G=!1;function Z(e,t){if(!e||G)return"";G=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(y){var r=y}Reflect.construct(e,[],t)}else{try{t.call()}catch(y){r=y}e.call(t.prototype)}else{try{throw Error()}catch(y){r=y}e()}}catch(y){if(y&&r&&typeof y.stack=="string"){for(var l=y.stack.split(`
`),o=r.stack.split(`
`),i=l.length-1,a=o.length-1;1<=i&&0<=a&&l[i]!==o[a];)a--;for(;1<=i&&0<=a;i--,a--)if(l[i]!==o[a]){if(i!==1||a!==1)do if(i--,a--,0>a||l[i]!==o[a]){var c=`
`+l[i].replace(" at new "," at ");return e.displayName&&c.includes("<anonymous>")&&(c=c.replace("<anonymous>",e.displayName)),c}while(1<=i&&0<=a);break}}}finally{G=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?C(e):""}function le(e){switch(e.tag){case 5:return C(e.type);case 16:return C("Lazy");case 13:return C("Suspense");case 19:return C("SuspenseList");case 0:case 2:case 15:return e=Z(e.type,!1),e;case 11:return e=Z(e.type.render,!1),e;case 1:return e=Z(e.type,!0),e;default:return""}}function ie(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case ze:return"Fragment";case Ee:return"Portal";case re:return"Profiler";case Le:return"StrictMode";case Ae:return"Suspense";case b:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case he:return(e.displayName||"Context")+".Consumer";case ye:return(e._context.displayName||"Context")+".Provider";case X:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case fe:return t=e.displayName||null,t!==null?t:ie(e.type)||"Memo";case ce:t=e._payload,e=e._init;try{return ie(e(t))}catch{}}return null}function me(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return ie(t);case 8:return t===Le?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function de(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function ge(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Ye(e){var t=ge(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var l=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return l.call(this)},set:function(i){r=""+i,o.call(this,i)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(i){r=""+i},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function yn(e){e._valueTracker||(e._valueTracker=Ye(e))}function Ot(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=ge(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Or(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Gl(e,t){var n=t.checked;return R({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function qi(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=de(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function bi(e,t){t=t.checked,t!=null&&Ce(e,"checked",t,!1)}function Yl(e,t){bi(e,t);var n=de(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Xl(e,t.type,n):t.hasOwnProperty("defaultValue")&&Xl(e,t.type,de(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function es(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Xl(e,t,n){(t!=="number"||Or(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Gn=Array.isArray;function wn(e,t,n,r){if(e=e.options,t){t={};for(var l=0;l<n.length;l++)t["$"+n[l]]=!0;for(n=0;n<e.length;n++)l=t.hasOwnProperty("$"+e[n].value),e[n].selected!==l&&(e[n].selected=l),l&&r&&(e[n].defaultSelected=!0)}else{for(n=""+de(n),t=null,l=0;l<e.length;l++){if(e[l].value===n){e[l].selected=!0,r&&(e[l].defaultSelected=!0);return}t!==null||e[l].disabled||(t=e[l])}t!==null&&(t.selected=!0)}}function Jl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(u(91));return R({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ts(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(u(92));if(Gn(n)){if(1<n.length)throw Error(u(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:de(n)}}function ns(e,t){var n=de(t.value),r=de(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function rs(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function ls(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Zl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?ls(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Ar,os=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,l){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,l)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Ar=Ar||document.createElement("div"),Ar.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Ar.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Yn(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Xn={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Nc=["Webkit","ms","Moz","O"];Object.keys(Xn).forEach(function(e){Nc.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Xn[t]=Xn[e]})});function is(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Xn.hasOwnProperty(e)&&Xn[e]?(""+t).trim():t+"px"}function ss(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,l=is(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,l):e[n]=l}}var Pc=R({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ql(e,t){if(t){if(Pc[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(u(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(u(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(u(61))}if(t.style!=null&&typeof t.style!="object")throw Error(u(62))}}function bl(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var eo=null;function to(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var no=null,xn=null,Sn=null;function us(e){if(e=vr(e)){if(typeof no!="function")throw Error(u(280));var t=e.stateNode;t&&(t=ol(t),no(e.stateNode,e.type,t))}}function as(e){xn?Sn?Sn.push(e):Sn=[e]:xn=e}function cs(){if(xn){var e=xn,t=Sn;if(Sn=xn=null,us(e),t)for(e=0;e<t.length;e++)us(t[e])}}function ds(e,t){return e(t)}function fs(){}var ro=!1;function ps(e,t,n){if(ro)return e(t,n);ro=!0;try{return ds(e,t,n)}finally{ro=!1,(xn!==null||Sn!==null)&&(fs(),cs())}}function Jn(e,t){var n=e.stateNode;if(n===null)return null;var r=ol(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(u(231,t,typeof n));return n}var lo=!1;if(S)try{var Zn={};Object.defineProperty(Zn,"passive",{get:function(){lo=!0}}),window.addEventListener("test",Zn,Zn),window.removeEventListener("test",Zn,Zn)}catch{lo=!1}function zc(e,t,n,r,l,o,i,a,c){var y=Array.prototype.slice.call(arguments,3);try{t.apply(n,y)}catch(_){this.onError(_)}}var qn=!1,Dr=null,Fr=!1,oo=null,jc={onError:function(e){qn=!0,Dr=e}};function Rc(e,t,n,r,l,o,i,a,c){qn=!1,Dr=null,zc.apply(jc,arguments)}function Ic(e,t,n,r,l,o,i,a,c){if(Rc.apply(this,arguments),qn){if(qn){var y=Dr;qn=!1,Dr=null}else throw Error(u(198));Fr||(Fr=!0,oo=y)}}function ln(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function ms(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function hs(e){if(ln(e)!==e)throw Error(u(188))}function Tc(e){var t=e.alternate;if(!t){if(t=ln(e),t===null)throw Error(u(188));return t!==e?null:e}for(var n=e,r=t;;){var l=n.return;if(l===null)break;var o=l.alternate;if(o===null){if(r=l.return,r!==null){n=r;continue}break}if(l.child===o.child){for(o=l.child;o;){if(o===n)return hs(l),e;if(o===r)return hs(l),t;o=o.sibling}throw Error(u(188))}if(n.return!==r.return)n=l,r=o;else{for(var i=!1,a=l.child;a;){if(a===n){i=!0,n=l,r=o;break}if(a===r){i=!0,r=l,n=o;break}a=a.sibling}if(!i){for(a=o.child;a;){if(a===n){i=!0,n=o,r=l;break}if(a===r){i=!0,r=o,n=l;break}a=a.sibling}if(!i)throw Error(u(189))}}if(n.alternate!==r)throw Error(u(190))}if(n.tag!==3)throw Error(u(188));return n.stateNode.current===n?e:t}function gs(e){return e=Tc(e),e!==null?vs(e):null}function vs(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=vs(e);if(t!==null)return t;e=e.sibling}return null}var ys=d.unstable_scheduleCallback,ws=d.unstable_cancelCallback,Lc=d.unstable_shouldYield,Mc=d.unstable_requestPaint,je=d.unstable_now,Oc=d.unstable_getCurrentPriorityLevel,io=d.unstable_ImmediatePriority,xs=d.unstable_UserBlockingPriority,Ur=d.unstable_NormalPriority,Ac=d.unstable_LowPriority,Ss=d.unstable_IdlePriority,Vr=null,xt=null;function Dc(e){if(xt&&typeof xt.onCommitFiberRoot=="function")try{xt.onCommitFiberRoot(Vr,e,void 0,(e.current.flags&128)===128)}catch{}}var ft=Math.clz32?Math.clz32:Vc,Fc=Math.log,Uc=Math.LN2;function Vc(e){return e>>>=0,e===0?32:31-(Fc(e)/Uc|0)|0}var $r=64,Wr=4194304;function bn(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Br(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,l=e.suspendedLanes,o=e.pingedLanes,i=n&268435455;if(i!==0){var a=i&~l;a!==0?r=bn(a):(o&=i,o!==0&&(r=bn(o)))}else i=n&~l,i!==0?r=bn(i):o!==0&&(r=bn(o));if(r===0)return 0;if(t!==0&&t!==r&&(t&l)===0&&(l=r&-r,o=t&-t,l>=o||l===16&&(o&4194240)!==0))return t;if((r&4)!==0&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-ft(t),l=1<<n,r|=e[n],t&=~l;return r}function $c(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Wc(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,l=e.expirationTimes,o=e.pendingLanes;0<o;){var i=31-ft(o),a=1<<i,c=l[i];c===-1?((a&n)===0||(a&r)!==0)&&(l[i]=$c(a,t)):c<=t&&(e.expiredLanes|=a),o&=~a}}function so(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function ks(){var e=$r;return $r<<=1,($r&4194240)===0&&($r=64),e}function uo(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function er(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-ft(t),e[t]=n}function Bc(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var l=31-ft(n),o=1<<l;t[l]=0,r[l]=-1,e[l]=-1,n&=~o}}function ao(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-ft(n),l=1<<r;l&t|e[r]&t&&(e[r]|=t),n&=~l}}var pe=0;function Cs(e){return e&=-e,1<e?4<e?(e&268435455)!==0?16:536870912:4:1}var Es,co,_s,Ns,Ps,fo=!1,Hr=[],At=null,Dt=null,Ft=null,tr=new Map,nr=new Map,Ut=[],Hc="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function zs(e,t){switch(e){case"focusin":case"focusout":At=null;break;case"dragenter":case"dragleave":Dt=null;break;case"mouseover":case"mouseout":Ft=null;break;case"pointerover":case"pointerout":tr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":nr.delete(t.pointerId)}}function rr(e,t,n,r,l,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[l]},t!==null&&(t=vr(t),t!==null&&co(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,l!==null&&t.indexOf(l)===-1&&t.push(l),e)}function Qc(e,t,n,r,l){switch(t){case"focusin":return At=rr(At,e,t,n,r,l),!0;case"dragenter":return Dt=rr(Dt,e,t,n,r,l),!0;case"mouseover":return Ft=rr(Ft,e,t,n,r,l),!0;case"pointerover":var o=l.pointerId;return tr.set(o,rr(tr.get(o)||null,e,t,n,r,l)),!0;case"gotpointercapture":return o=l.pointerId,nr.set(o,rr(nr.get(o)||null,e,t,n,r,l)),!0}return!1}function js(e){var t=on(e.target);if(t!==null){var n=ln(t);if(n!==null){if(t=n.tag,t===13){if(t=ms(n),t!==null){e.blockedOn=t,Ps(e.priority,function(){_s(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Qr(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=mo(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);eo=r,n.target.dispatchEvent(r),eo=null}else return t=vr(n),t!==null&&co(t),e.blockedOn=n,!1;t.shift()}return!0}function Rs(e,t,n){Qr(e)&&n.delete(t)}function Kc(){fo=!1,At!==null&&Qr(At)&&(At=null),Dt!==null&&Qr(Dt)&&(Dt=null),Ft!==null&&Qr(Ft)&&(Ft=null),tr.forEach(Rs),nr.forEach(Rs)}function lr(e,t){e.blockedOn===t&&(e.blockedOn=null,fo||(fo=!0,d.unstable_scheduleCallback(d.unstable_NormalPriority,Kc)))}function or(e){function t(l){return lr(l,e)}if(0<Hr.length){lr(Hr[0],e);for(var n=1;n<Hr.length;n++){var r=Hr[n];r.blockedOn===e&&(r.blockedOn=null)}}for(At!==null&&lr(At,e),Dt!==null&&lr(Dt,e),Ft!==null&&lr(Ft,e),tr.forEach(t),nr.forEach(t),n=0;n<Ut.length;n++)r=Ut[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Ut.length&&(n=Ut[0],n.blockedOn===null);)js(n),n.blockedOn===null&&Ut.shift()}var kn=ue.ReactCurrentBatchConfig,Kr=!0;function Gc(e,t,n,r){var l=pe,o=kn.transition;kn.transition=null;try{pe=1,po(e,t,n,r)}finally{pe=l,kn.transition=o}}function Yc(e,t,n,r){var l=pe,o=kn.transition;kn.transition=null;try{pe=4,po(e,t,n,r)}finally{pe=l,kn.transition=o}}function po(e,t,n,r){if(Kr){var l=mo(e,t,n,r);if(l===null)Io(e,t,r,Gr,n),zs(e,r);else if(Qc(l,e,t,n,r))r.stopPropagation();else if(zs(e,r),t&4&&-1<Hc.indexOf(e)){for(;l!==null;){var o=vr(l);if(o!==null&&Es(o),o=mo(e,t,n,r),o===null&&Io(e,t,r,Gr,n),o===l)break;l=o}l!==null&&r.stopPropagation()}else Io(e,t,r,null,n)}}var Gr=null;function mo(e,t,n,r){if(Gr=null,e=to(r),e=on(e),e!==null)if(t=ln(e),t===null)e=null;else if(n=t.tag,n===13){if(e=ms(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Gr=e,null}function Is(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Oc()){case io:return 1;case xs:return 4;case Ur:case Ac:return 16;case Ss:return 536870912;default:return 16}default:return 16}}var Vt=null,ho=null,Yr=null;function Ts(){if(Yr)return Yr;var e,t=ho,n=t.length,r,l="value"in Vt?Vt.value:Vt.textContent,o=l.length;for(e=0;e<n&&t[e]===l[e];e++);var i=n-e;for(r=1;r<=i&&t[n-r]===l[o-r];r++);return Yr=l.slice(e,1<r?1-r:void 0)}function Xr(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Jr(){return!0}function Ls(){return!1}function tt(e){function t(n,r,l,o,i){this._reactName=n,this._targetInst=l,this.type=r,this.nativeEvent=o,this.target=i,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(o):o[a]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?Jr:Ls,this.isPropagationStopped=Ls,this}return R(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Jr)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Jr)},persist:function(){},isPersistent:Jr}),t}var Cn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},go=tt(Cn),ir=R({},Cn,{view:0,detail:0}),Xc=tt(ir),vo,yo,sr,Zr=R({},ir,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:xo,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==sr&&(sr&&e.type==="mousemove"?(vo=e.screenX-sr.screenX,yo=e.screenY-sr.screenY):yo=vo=0,sr=e),vo)},movementY:function(e){return"movementY"in e?e.movementY:yo}}),Ms=tt(Zr),Jc=R({},Zr,{dataTransfer:0}),Zc=tt(Jc),qc=R({},ir,{relatedTarget:0}),wo=tt(qc),bc=R({},Cn,{animationName:0,elapsedTime:0,pseudoElement:0}),ed=tt(bc),td=R({},Cn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),nd=tt(td),rd=R({},Cn,{data:0}),Os=tt(rd),ld={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},od={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},id={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function sd(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=id[e])?!!t[e]:!1}function xo(){return sd}var ud=R({},ir,{key:function(e){if(e.key){var t=ld[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Xr(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?od[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:xo,charCode:function(e){return e.type==="keypress"?Xr(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Xr(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),ad=tt(ud),cd=R({},Zr,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),As=tt(cd),dd=R({},ir,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:xo}),fd=tt(dd),pd=R({},Cn,{propertyName:0,elapsedTime:0,pseudoElement:0}),md=tt(pd),hd=R({},Zr,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),gd=tt(hd),vd=[9,13,27,32],So=S&&"CompositionEvent"in window,ur=null;S&&"documentMode"in document&&(ur=document.documentMode);var yd=S&&"TextEvent"in window&&!ur,Ds=S&&(!So||ur&&8<ur&&11>=ur),Fs=" ",Us=!1;function Vs(e,t){switch(e){case"keyup":return vd.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function $s(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var En=!1;function wd(e,t){switch(e){case"compositionend":return $s(t);case"keypress":return t.which!==32?null:(Us=!0,Fs);case"textInput":return e=t.data,e===Fs&&Us?null:e;default:return null}}function xd(e,t){if(En)return e==="compositionend"||!So&&Vs(e,t)?(e=Ts(),Yr=ho=Vt=null,En=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Ds&&t.locale!=="ko"?null:t.data;default:return null}}var Sd={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Ws(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Sd[e.type]:t==="textarea"}function Bs(e,t,n,r){as(r),t=nl(t,"onChange"),0<t.length&&(n=new go("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var ar=null,cr=null;function kd(e){su(e,0)}function qr(e){var t=jn(e);if(Ot(t))return e}function Cd(e,t){if(e==="change")return t}var Hs=!1;if(S){var ko;if(S){var Co="oninput"in document;if(!Co){var Qs=document.createElement("div");Qs.setAttribute("oninput","return;"),Co=typeof Qs.oninput=="function"}ko=Co}else ko=!1;Hs=ko&&(!document.documentMode||9<document.documentMode)}function Ks(){ar&&(ar.detachEvent("onpropertychange",Gs),cr=ar=null)}function Gs(e){if(e.propertyName==="value"&&qr(cr)){var t=[];Bs(t,cr,e,to(e)),ps(kd,t)}}function Ed(e,t,n){e==="focusin"?(Ks(),ar=t,cr=n,ar.attachEvent("onpropertychange",Gs)):e==="focusout"&&Ks()}function _d(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return qr(cr)}function Nd(e,t){if(e==="click")return qr(t)}function Pd(e,t){if(e==="input"||e==="change")return qr(t)}function zd(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var pt=typeof Object.is=="function"?Object.is:zd;function dr(e,t){if(pt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var l=n[r];if(!k.call(t,l)||!pt(e[l],t[l]))return!1}return!0}function Ys(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Xs(e,t){var n=Ys(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Ys(n)}}function Js(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Js(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Zs(){for(var e=window,t=Or();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Or(e.document)}return t}function Eo(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function jd(e){var t=Zs(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Js(n.ownerDocument.documentElement,n)){if(r!==null&&Eo(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var l=n.textContent.length,o=Math.min(r.start,l);r=r.end===void 0?o:Math.min(r.end,l),!e.extend&&o>r&&(l=r,r=o,o=l),l=Xs(n,o);var i=Xs(n,r);l&&i&&(e.rangeCount!==1||e.anchorNode!==l.node||e.anchorOffset!==l.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&(t=t.createRange(),t.setStart(l.node,l.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Rd=S&&"documentMode"in document&&11>=document.documentMode,_n=null,_o=null,fr=null,No=!1;function qs(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;No||_n==null||_n!==Or(r)||(r=_n,"selectionStart"in r&&Eo(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),fr&&dr(fr,r)||(fr=r,r=nl(_o,"onSelect"),0<r.length&&(t=new go("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=_n)))}function br(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Nn={animationend:br("Animation","AnimationEnd"),animationiteration:br("Animation","AnimationIteration"),animationstart:br("Animation","AnimationStart"),transitionend:br("Transition","TransitionEnd")},Po={},bs={};S&&(bs=document.createElement("div").style,"AnimationEvent"in window||(delete Nn.animationend.animation,delete Nn.animationiteration.animation,delete Nn.animationstart.animation),"TransitionEvent"in window||delete Nn.transitionend.transition);function el(e){if(Po[e])return Po[e];if(!Nn[e])return e;var t=Nn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in bs)return Po[e]=t[n];return e}var eu=el("animationend"),tu=el("animationiteration"),nu=el("animationstart"),ru=el("transitionend"),lu=new Map,ou="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function $t(e,t){lu.set(e,t),x(t,[e])}for(var zo=0;zo<ou.length;zo++){var jo=ou[zo],Id=jo.toLowerCase(),Td=jo[0].toUpperCase()+jo.slice(1);$t(Id,"on"+Td)}$t(eu,"onAnimationEnd"),$t(tu,"onAnimationIteration"),$t(nu,"onAnimationStart"),$t("dblclick","onDoubleClick"),$t("focusin","onFocus"),$t("focusout","onBlur"),$t(ru,"onTransitionEnd"),N("onMouseEnter",["mouseout","mouseover"]),N("onMouseLeave",["mouseout","mouseover"]),N("onPointerEnter",["pointerout","pointerover"]),N("onPointerLeave",["pointerout","pointerover"]),x("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),x("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),x("onBeforeInput",["compositionend","keypress","textInput","paste"]),x("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),x("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),x("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var pr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Ld=new Set("cancel close invalid load scroll toggle".split(" ").concat(pr));function iu(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Ic(r,t,void 0,e),e.currentTarget=null}function su(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],l=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var i=r.length-1;0<=i;i--){var a=r[i],c=a.instance,y=a.currentTarget;if(a=a.listener,c!==o&&l.isPropagationStopped())break e;iu(l,a,y),o=c}else for(i=0;i<r.length;i++){if(a=r[i],c=a.instance,y=a.currentTarget,a=a.listener,c!==o&&l.isPropagationStopped())break e;iu(l,a,y),o=c}}}if(Fr)throw e=oo,Fr=!1,oo=null,e}function we(e,t){var n=t[Do];n===void 0&&(n=t[Do]=new Set);var r=e+"__bubble";n.has(r)||(uu(t,e,2,!1),n.add(r))}function Ro(e,t,n){var r=0;t&&(r|=4),uu(n,e,r,t)}var tl="_reactListening"+Math.random().toString(36).slice(2);function mr(e){if(!e[tl]){e[tl]=!0,v.forEach(function(n){n!=="selectionchange"&&(Ld.has(n)||Ro(n,!1,e),Ro(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[tl]||(t[tl]=!0,Ro("selectionchange",!1,t))}}function uu(e,t,n,r){switch(Is(t)){case 1:var l=Gc;break;case 4:l=Yc;break;default:l=po}n=l.bind(null,t,n,e),l=void 0,!lo||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(l=!0),r?l!==void 0?e.addEventListener(t,n,{capture:!0,passive:l}):e.addEventListener(t,n,!0):l!==void 0?e.addEventListener(t,n,{passive:l}):e.addEventListener(t,n,!1)}function Io(e,t,n,r,l){var o=r;if((t&1)===0&&(t&2)===0&&r!==null)e:for(;;){if(r===null)return;var i=r.tag;if(i===3||i===4){var a=r.stateNode.containerInfo;if(a===l||a.nodeType===8&&a.parentNode===l)break;if(i===4)for(i=r.return;i!==null;){var c=i.tag;if((c===3||c===4)&&(c=i.stateNode.containerInfo,c===l||c.nodeType===8&&c.parentNode===l))return;i=i.return}for(;a!==null;){if(i=on(a),i===null)return;if(c=i.tag,c===5||c===6){r=o=i;continue e}a=a.parentNode}}r=r.return}ps(function(){var y=o,_=to(n),z=[];e:{var E=lu.get(e);if(E!==void 0){var T=go,O=e;switch(e){case"keypress":if(Xr(n)===0)break e;case"keydown":case"keyup":T=ad;break;case"focusin":O="focus",T=wo;break;case"focusout":O="blur",T=wo;break;case"beforeblur":case"afterblur":T=wo;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":T=Ms;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":T=Zc;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":T=fd;break;case eu:case tu:case nu:T=ed;break;case ru:T=md;break;case"scroll":T=Xc;break;case"wheel":T=gd;break;case"copy":case"cut":case"paste":T=nd;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":T=As}var U=(t&4)!==0,Re=!U&&e==="scroll",m=U?E!==null?E+"Capture":null:E;U=[];for(var f=y,h;f!==null;){h=f;var j=h.stateNode;if(h.tag===5&&j!==null&&(h=j,m!==null&&(j=Jn(f,m),j!=null&&U.push(hr(f,j,h)))),Re)break;f=f.return}0<U.length&&(E=new T(E,O,null,n,_),z.push({event:E,listeners:U}))}}if((t&7)===0){e:{if(E=e==="mouseover"||e==="pointerover",T=e==="mouseout"||e==="pointerout",E&&n!==eo&&(O=n.relatedTarget||n.fromElement)&&(on(O)||O[_t]))break e;if((T||E)&&(E=_.window===_?_:(E=_.ownerDocument)?E.defaultView||E.parentWindow:window,T?(O=n.relatedTarget||n.toElement,T=y,O=O?on(O):null,O!==null&&(Re=ln(O),O!==Re||O.tag!==5&&O.tag!==6)&&(O=null)):(T=null,O=y),T!==O)){if(U=Ms,j="onMouseLeave",m="onMouseEnter",f="mouse",(e==="pointerout"||e==="pointerover")&&(U=As,j="onPointerLeave",m="onPointerEnter",f="pointer"),Re=T==null?E:jn(T),h=O==null?E:jn(O),E=new U(j,f+"leave",T,n,_),E.target=Re,E.relatedTarget=h,j=null,on(_)===y&&(U=new U(m,f+"enter",O,n,_),U.target=h,U.relatedTarget=Re,j=U),Re=j,T&&O)t:{for(U=T,m=O,f=0,h=U;h;h=Pn(h))f++;for(h=0,j=m;j;j=Pn(j))h++;for(;0<f-h;)U=Pn(U),f--;for(;0<h-f;)m=Pn(m),h--;for(;f--;){if(U===m||m!==null&&U===m.alternate)break t;U=Pn(U),m=Pn(m)}U=null}else U=null;T!==null&&au(z,E,T,U,!1),O!==null&&Re!==null&&au(z,Re,O,U,!0)}}e:{if(E=y?jn(y):window,T=E.nodeName&&E.nodeName.toLowerCase(),T==="select"||T==="input"&&E.type==="file")var V=Cd;else if(Ws(E))if(Hs)V=Pd;else{V=_d;var H=Ed}else(T=E.nodeName)&&T.toLowerCase()==="input"&&(E.type==="checkbox"||E.type==="radio")&&(V=Nd);if(V&&(V=V(e,y))){Bs(z,V,n,_);break e}H&&H(e,E,y),e==="focusout"&&(H=E._wrapperState)&&H.controlled&&E.type==="number"&&Xl(E,"number",E.value)}switch(H=y?jn(y):window,e){case"focusin":(Ws(H)||H.contentEditable==="true")&&(_n=H,_o=y,fr=null);break;case"focusout":fr=_o=_n=null;break;case"mousedown":No=!0;break;case"contextmenu":case"mouseup":case"dragend":No=!1,qs(z,n,_);break;case"selectionchange":if(Rd)break;case"keydown":case"keyup":qs(z,n,_)}var Q;if(So)e:{switch(e){case"compositionstart":var K="onCompositionStart";break e;case"compositionend":K="onCompositionEnd";break e;case"compositionupdate":K="onCompositionUpdate";break e}K=void 0}else En?Vs(e,n)&&(K="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(K="onCompositionStart");K&&(Ds&&n.locale!=="ko"&&(En||K!=="onCompositionStart"?K==="onCompositionEnd"&&En&&(Q=Ts()):(Vt=_,ho="value"in Vt?Vt.value:Vt.textContent,En=!0)),H=nl(y,K),0<H.length&&(K=new Os(K,e,null,n,_),z.push({event:K,listeners:H}),Q?K.data=Q:(Q=$s(n),Q!==null&&(K.data=Q)))),(Q=yd?wd(e,n):xd(e,n))&&(y=nl(y,"onBeforeInput"),0<y.length&&(_=new Os("onBeforeInput","beforeinput",null,n,_),z.push({event:_,listeners:y}),_.data=Q))}su(z,t)})}function hr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function nl(e,t){for(var n=t+"Capture",r=[];e!==null;){var l=e,o=l.stateNode;l.tag===5&&o!==null&&(l=o,o=Jn(e,n),o!=null&&r.unshift(hr(e,o,l)),o=Jn(e,t),o!=null&&r.push(hr(e,o,l))),e=e.return}return r}function Pn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function au(e,t,n,r,l){for(var o=t._reactName,i=[];n!==null&&n!==r;){var a=n,c=a.alternate,y=a.stateNode;if(c!==null&&c===r)break;a.tag===5&&y!==null&&(a=y,l?(c=Jn(n,o),c!=null&&i.unshift(hr(n,c,a))):l||(c=Jn(n,o),c!=null&&i.push(hr(n,c,a)))),n=n.return}i.length!==0&&e.push({event:t,listeners:i})}var Md=/\r\n?/g,Od=/\u0000|\uFFFD/g;function cu(e){return(typeof e=="string"?e:""+e).replace(Md,`
`).replace(Od,"")}function rl(e,t,n){if(t=cu(t),cu(e)!==t&&n)throw Error(u(425))}function ll(){}var To=null,Lo=null;function Mo(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Oo=typeof setTimeout=="function"?setTimeout:void 0,Ad=typeof clearTimeout=="function"?clearTimeout:void 0,du=typeof Promise=="function"?Promise:void 0,Dd=typeof queueMicrotask=="function"?queueMicrotask:typeof du<"u"?function(e){return du.resolve(null).then(e).catch(Fd)}:Oo;function Fd(e){setTimeout(function(){throw e})}function Ao(e,t){var n=t,r=0;do{var l=n.nextSibling;if(e.removeChild(n),l&&l.nodeType===8)if(n=l.data,n==="/$"){if(r===0){e.removeChild(l),or(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=l}while(n);or(t)}function Wt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function fu(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var zn=Math.random().toString(36).slice(2),St="__reactFiber$"+zn,gr="__reactProps$"+zn,_t="__reactContainer$"+zn,Do="__reactEvents$"+zn,Ud="__reactListeners$"+zn,Vd="__reactHandles$"+zn;function on(e){var t=e[St];if(t)return t;for(var n=e.parentNode;n;){if(t=n[_t]||n[St]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=fu(e);e!==null;){if(n=e[St])return n;e=fu(e)}return t}e=n,n=e.parentNode}return null}function vr(e){return e=e[St]||e[_t],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function jn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(u(33))}function ol(e){return e[gr]||null}var Fo=[],Rn=-1;function Bt(e){return{current:e}}function xe(e){0>Rn||(e.current=Fo[Rn],Fo[Rn]=null,Rn--)}function ve(e,t){Rn++,Fo[Rn]=e.current,e.current=t}var Ht={},We=Bt(Ht),Xe=Bt(!1),sn=Ht;function In(e,t){var n=e.type.contextTypes;if(!n)return Ht;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var l={},o;for(o in n)l[o]=t[o];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function Je(e){return e=e.childContextTypes,e!=null}function il(){xe(Xe),xe(We)}function pu(e,t,n){if(We.current!==Ht)throw Error(u(168));ve(We,t),ve(Xe,n)}function mu(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var l in r)if(!(l in t))throw Error(u(108,me(e)||"Unknown",l));return R({},n,r)}function sl(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Ht,sn=We.current,ve(We,e),ve(Xe,Xe.current),!0}function hu(e,t,n){var r=e.stateNode;if(!r)throw Error(u(169));n?(e=mu(e,t,sn),r.__reactInternalMemoizedMergedChildContext=e,xe(Xe),xe(We),ve(We,e)):xe(Xe),ve(Xe,n)}var Nt=null,ul=!1,Uo=!1;function gu(e){Nt===null?Nt=[e]:Nt.push(e)}function $d(e){ul=!0,gu(e)}function Qt(){if(!Uo&&Nt!==null){Uo=!0;var e=0,t=pe;try{var n=Nt;for(pe=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Nt=null,ul=!1}catch(l){throw Nt!==null&&(Nt=Nt.slice(e+1)),ys(io,Qt),l}finally{pe=t,Uo=!1}}return null}var Tn=[],Ln=0,al=null,cl=0,it=[],st=0,un=null,Pt=1,zt="";function an(e,t){Tn[Ln++]=cl,Tn[Ln++]=al,al=e,cl=t}function vu(e,t,n){it[st++]=Pt,it[st++]=zt,it[st++]=un,un=e;var r=Pt;e=zt;var l=32-ft(r)-1;r&=~(1<<l),n+=1;var o=32-ft(t)+l;if(30<o){var i=l-l%5;o=(r&(1<<i)-1).toString(32),r>>=i,l-=i,Pt=1<<32-ft(t)+l|n<<l|r,zt=o+e}else Pt=1<<o|n<<l|r,zt=e}function Vo(e){e.return!==null&&(an(e,1),vu(e,1,0))}function $o(e){for(;e===al;)al=Tn[--Ln],Tn[Ln]=null,cl=Tn[--Ln],Tn[Ln]=null;for(;e===un;)un=it[--st],it[st]=null,zt=it[--st],it[st]=null,Pt=it[--st],it[st]=null}var nt=null,rt=null,ke=!1,mt=null;function yu(e,t){var n=dt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function wu(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,nt=e,rt=Wt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,nt=e,rt=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=un!==null?{id:Pt,overflow:zt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=dt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,nt=e,rt=null,!0):!1;default:return!1}}function Wo(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Bo(e){if(ke){var t=rt;if(t){var n=t;if(!wu(e,t)){if(Wo(e))throw Error(u(418));t=Wt(n.nextSibling);var r=nt;t&&wu(e,t)?yu(r,n):(e.flags=e.flags&-4097|2,ke=!1,nt=e)}}else{if(Wo(e))throw Error(u(418));e.flags=e.flags&-4097|2,ke=!1,nt=e}}}function xu(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;nt=e}function dl(e){if(e!==nt)return!1;if(!ke)return xu(e),ke=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Mo(e.type,e.memoizedProps)),t&&(t=rt)){if(Wo(e))throw Su(),Error(u(418));for(;t;)yu(e,t),t=Wt(t.nextSibling)}if(xu(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(u(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){rt=Wt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}rt=null}}else rt=nt?Wt(e.stateNode.nextSibling):null;return!0}function Su(){for(var e=rt;e;)e=Wt(e.nextSibling)}function Mn(){rt=nt=null,ke=!1}function Ho(e){mt===null?mt=[e]:mt.push(e)}var Wd=ue.ReactCurrentBatchConfig;function yr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(u(309));var r=n.stateNode}if(!r)throw Error(u(147,e));var l=r,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(i){var a=l.refs;i===null?delete a[o]:a[o]=i},t._stringRef=o,t)}if(typeof e!="string")throw Error(u(284));if(!n._owner)throw Error(u(290,e))}return e}function fl(e,t){throw e=Object.prototype.toString.call(t),Error(u(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function ku(e){var t=e._init;return t(e._payload)}function Cu(e){function t(m,f){if(e){var h=m.deletions;h===null?(m.deletions=[f],m.flags|=16):h.push(f)}}function n(m,f){if(!e)return null;for(;f!==null;)t(m,f),f=f.sibling;return null}function r(m,f){for(m=new Map;f!==null;)f.key!==null?m.set(f.key,f):m.set(f.index,f),f=f.sibling;return m}function l(m,f){return m=bt(m,f),m.index=0,m.sibling=null,m}function o(m,f,h){return m.index=h,e?(h=m.alternate,h!==null?(h=h.index,h<f?(m.flags|=2,f):h):(m.flags|=2,f)):(m.flags|=1048576,f)}function i(m){return e&&m.alternate===null&&(m.flags|=2),m}function a(m,f,h,j){return f===null||f.tag!==6?(f=Oi(h,m.mode,j),f.return=m,f):(f=l(f,h),f.return=m,f)}function c(m,f,h,j){var V=h.type;return V===ze?_(m,f,h.props.children,j,h.key):f!==null&&(f.elementType===V||typeof V=="object"&&V!==null&&V.$$typeof===ce&&ku(V)===f.type)?(j=l(f,h.props),j.ref=yr(m,f,h),j.return=m,j):(j=Al(h.type,h.key,h.props,null,m.mode,j),j.ref=yr(m,f,h),j.return=m,j)}function y(m,f,h,j){return f===null||f.tag!==4||f.stateNode.containerInfo!==h.containerInfo||f.stateNode.implementation!==h.implementation?(f=Ai(h,m.mode,j),f.return=m,f):(f=l(f,h.children||[]),f.return=m,f)}function _(m,f,h,j,V){return f===null||f.tag!==7?(f=vn(h,m.mode,j,V),f.return=m,f):(f=l(f,h),f.return=m,f)}function z(m,f,h){if(typeof f=="string"&&f!==""||typeof f=="number")return f=Oi(""+f,m.mode,h),f.return=m,f;if(typeof f=="object"&&f!==null){switch(f.$$typeof){case Ie:return h=Al(f.type,f.key,f.props,null,m.mode,h),h.ref=yr(m,null,f),h.return=m,h;case Ee:return f=Ai(f,m.mode,h),f.return=m,f;case ce:var j=f._init;return z(m,j(f._payload),h)}if(Gn(f)||F(f))return f=vn(f,m.mode,h,null),f.return=m,f;fl(m,f)}return null}function E(m,f,h,j){var V=f!==null?f.key:null;if(typeof h=="string"&&h!==""||typeof h=="number")return V!==null?null:a(m,f,""+h,j);if(typeof h=="object"&&h!==null){switch(h.$$typeof){case Ie:return h.key===V?c(m,f,h,j):null;case Ee:return h.key===V?y(m,f,h,j):null;case ce:return V=h._init,E(m,f,V(h._payload),j)}if(Gn(h)||F(h))return V!==null?null:_(m,f,h,j,null);fl(m,h)}return null}function T(m,f,h,j,V){if(typeof j=="string"&&j!==""||typeof j=="number")return m=m.get(h)||null,a(f,m,""+j,V);if(typeof j=="object"&&j!==null){switch(j.$$typeof){case Ie:return m=m.get(j.key===null?h:j.key)||null,c(f,m,j,V);case Ee:return m=m.get(j.key===null?h:j.key)||null,y(f,m,j,V);case ce:var H=j._init;return T(m,f,h,H(j._payload),V)}if(Gn(j)||F(j))return m=m.get(h)||null,_(f,m,j,V,null);fl(f,j)}return null}function O(m,f,h,j){for(var V=null,H=null,Q=f,K=f=0,Ue=null;Q!==null&&K<h.length;K++){Q.index>K?(Ue=Q,Q=null):Ue=Q.sibling;var ae=E(m,Q,h[K],j);if(ae===null){Q===null&&(Q=Ue);break}e&&Q&&ae.alternate===null&&t(m,Q),f=o(ae,f,K),H===null?V=ae:H.sibling=ae,H=ae,Q=Ue}if(K===h.length)return n(m,Q),ke&&an(m,K),V;if(Q===null){for(;K<h.length;K++)Q=z(m,h[K],j),Q!==null&&(f=o(Q,f,K),H===null?V=Q:H.sibling=Q,H=Q);return ke&&an(m,K),V}for(Q=r(m,Q);K<h.length;K++)Ue=T(Q,m,K,h[K],j),Ue!==null&&(e&&Ue.alternate!==null&&Q.delete(Ue.key===null?K:Ue.key),f=o(Ue,f,K),H===null?V=Ue:H.sibling=Ue,H=Ue);return e&&Q.forEach(function(en){return t(m,en)}),ke&&an(m,K),V}function U(m,f,h,j){var V=F(h);if(typeof V!="function")throw Error(u(150));if(h=V.call(h),h==null)throw Error(u(151));for(var H=V=null,Q=f,K=f=0,Ue=null,ae=h.next();Q!==null&&!ae.done;K++,ae=h.next()){Q.index>K?(Ue=Q,Q=null):Ue=Q.sibling;var en=E(m,Q,ae.value,j);if(en===null){Q===null&&(Q=Ue);break}e&&Q&&en.alternate===null&&t(m,Q),f=o(en,f,K),H===null?V=en:H.sibling=en,H=en,Q=Ue}if(ae.done)return n(m,Q),ke&&an(m,K),V;if(Q===null){for(;!ae.done;K++,ae=h.next())ae=z(m,ae.value,j),ae!==null&&(f=o(ae,f,K),H===null?V=ae:H.sibling=ae,H=ae);return ke&&an(m,K),V}for(Q=r(m,Q);!ae.done;K++,ae=h.next())ae=T(Q,m,K,ae.value,j),ae!==null&&(e&&ae.alternate!==null&&Q.delete(ae.key===null?K:ae.key),f=o(ae,f,K),H===null?V=ae:H.sibling=ae,H=ae);return e&&Q.forEach(function(kf){return t(m,kf)}),ke&&an(m,K),V}function Re(m,f,h,j){if(typeof h=="object"&&h!==null&&h.type===ze&&h.key===null&&(h=h.props.children),typeof h=="object"&&h!==null){switch(h.$$typeof){case Ie:e:{for(var V=h.key,H=f;H!==null;){if(H.key===V){if(V=h.type,V===ze){if(H.tag===7){n(m,H.sibling),f=l(H,h.props.children),f.return=m,m=f;break e}}else if(H.elementType===V||typeof V=="object"&&V!==null&&V.$$typeof===ce&&ku(V)===H.type){n(m,H.sibling),f=l(H,h.props),f.ref=yr(m,H,h),f.return=m,m=f;break e}n(m,H);break}else t(m,H);H=H.sibling}h.type===ze?(f=vn(h.props.children,m.mode,j,h.key),f.return=m,m=f):(j=Al(h.type,h.key,h.props,null,m.mode,j),j.ref=yr(m,f,h),j.return=m,m=j)}return i(m);case Ee:e:{for(H=h.key;f!==null;){if(f.key===H)if(f.tag===4&&f.stateNode.containerInfo===h.containerInfo&&f.stateNode.implementation===h.implementation){n(m,f.sibling),f=l(f,h.children||[]),f.return=m,m=f;break e}else{n(m,f);break}else t(m,f);f=f.sibling}f=Ai(h,m.mode,j),f.return=m,m=f}return i(m);case ce:return H=h._init,Re(m,f,H(h._payload),j)}if(Gn(h))return O(m,f,h,j);if(F(h))return U(m,f,h,j);fl(m,h)}return typeof h=="string"&&h!==""||typeof h=="number"?(h=""+h,f!==null&&f.tag===6?(n(m,f.sibling),f=l(f,h),f.return=m,m=f):(n(m,f),f=Oi(h,m.mode,j),f.return=m,m=f),i(m)):n(m,f)}return Re}var On=Cu(!0),Eu=Cu(!1),pl=Bt(null),ml=null,An=null,Qo=null;function Ko(){Qo=An=ml=null}function Go(e){var t=pl.current;xe(pl),e._currentValue=t}function Yo(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Dn(e,t){ml=e,Qo=An=null,e=e.dependencies,e!==null&&e.firstContext!==null&&((e.lanes&t)!==0&&(Ze=!0),e.firstContext=null)}function ut(e){var t=e._currentValue;if(Qo!==e)if(e={context:e,memoizedValue:t,next:null},An===null){if(ml===null)throw Error(u(308));An=e,ml.dependencies={lanes:0,firstContext:e}}else An=An.next=e;return t}var cn=null;function Xo(e){cn===null?cn=[e]:cn.push(e)}function _u(e,t,n,r){var l=t.interleaved;return l===null?(n.next=n,Xo(t)):(n.next=l.next,l.next=n),t.interleaved=n,jt(e,r)}function jt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Kt=!1;function Jo(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Nu(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Rt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Gt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,(se&2)!==0){var l=r.pending;return l===null?t.next=t:(t.next=l.next,l.next=t),r.pending=t,jt(e,n)}return l=r.interleaved,l===null?(t.next=t,Xo(r)):(t.next=l.next,l.next=t),r.interleaved=t,jt(e,n)}function hl(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,ao(e,n)}}function Pu(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var l=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?l=o=i:o=o.next=i,n=n.next}while(n!==null);o===null?l=o=t:o=o.next=t}else l=o=t;n={baseState:r.baseState,firstBaseUpdate:l,lastBaseUpdate:o,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function gl(e,t,n,r){var l=e.updateQueue;Kt=!1;var o=l.firstBaseUpdate,i=l.lastBaseUpdate,a=l.shared.pending;if(a!==null){l.shared.pending=null;var c=a,y=c.next;c.next=null,i===null?o=y:i.next=y,i=c;var _=e.alternate;_!==null&&(_=_.updateQueue,a=_.lastBaseUpdate,a!==i&&(a===null?_.firstBaseUpdate=y:a.next=y,_.lastBaseUpdate=c))}if(o!==null){var z=l.baseState;i=0,_=y=c=null,a=o;do{var E=a.lane,T=a.eventTime;if((r&E)===E){_!==null&&(_=_.next={eventTime:T,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var O=e,U=a;switch(E=t,T=n,U.tag){case 1:if(O=U.payload,typeof O=="function"){z=O.call(T,z,E);break e}z=O;break e;case 3:O.flags=O.flags&-65537|128;case 0:if(O=U.payload,E=typeof O=="function"?O.call(T,z,E):O,E==null)break e;z=R({},z,E);break e;case 2:Kt=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,E=l.effects,E===null?l.effects=[a]:E.push(a))}else T={eventTime:T,lane:E,tag:a.tag,payload:a.payload,callback:a.callback,next:null},_===null?(y=_=T,c=z):_=_.next=T,i|=E;if(a=a.next,a===null){if(a=l.shared.pending,a===null)break;E=a,a=E.next,E.next=null,l.lastBaseUpdate=E,l.shared.pending=null}}while(!0);if(_===null&&(c=z),l.baseState=c,l.firstBaseUpdate=y,l.lastBaseUpdate=_,t=l.shared.interleaved,t!==null){l=t;do i|=l.lane,l=l.next;while(l!==t)}else o===null&&(l.shared.lanes=0);pn|=i,e.lanes=i,e.memoizedState=z}}function zu(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],l=r.callback;if(l!==null){if(r.callback=null,r=n,typeof l!="function")throw Error(u(191,l));l.call(r)}}}var wr={},kt=Bt(wr),xr=Bt(wr),Sr=Bt(wr);function dn(e){if(e===wr)throw Error(u(174));return e}function Zo(e,t){switch(ve(Sr,t),ve(xr,e),ve(kt,wr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Zl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Zl(t,e)}xe(kt),ve(kt,t)}function Fn(){xe(kt),xe(xr),xe(Sr)}function ju(e){dn(Sr.current);var t=dn(kt.current),n=Zl(t,e.type);t!==n&&(ve(xr,e),ve(kt,n))}function qo(e){xr.current===e&&(xe(kt),xe(xr))}var _e=Bt(0);function vl(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var bo=[];function ei(){for(var e=0;e<bo.length;e++)bo[e]._workInProgressVersionPrimary=null;bo.length=0}var yl=ue.ReactCurrentDispatcher,ti=ue.ReactCurrentBatchConfig,fn=0,Ne=null,Me=null,De=null,wl=!1,kr=!1,Cr=0,Bd=0;function Be(){throw Error(u(321))}function ni(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!pt(e[n],t[n]))return!1;return!0}function ri(e,t,n,r,l,o){if(fn=o,Ne=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,yl.current=e===null||e.memoizedState===null?Gd:Yd,e=n(r,l),kr){o=0;do{if(kr=!1,Cr=0,25<=o)throw Error(u(301));o+=1,De=Me=null,t.updateQueue=null,yl.current=Xd,e=n(r,l)}while(kr)}if(yl.current=kl,t=Me!==null&&Me.next!==null,fn=0,De=Me=Ne=null,wl=!1,t)throw Error(u(300));return e}function li(){var e=Cr!==0;return Cr=0,e}function Ct(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return De===null?Ne.memoizedState=De=e:De=De.next=e,De}function at(){if(Me===null){var e=Ne.alternate;e=e!==null?e.memoizedState:null}else e=Me.next;var t=De===null?Ne.memoizedState:De.next;if(t!==null)De=t,Me=e;else{if(e===null)throw Error(u(310));Me=e,e={memoizedState:Me.memoizedState,baseState:Me.baseState,baseQueue:Me.baseQueue,queue:Me.queue,next:null},De===null?Ne.memoizedState=De=e:De=De.next=e}return De}function Er(e,t){return typeof t=="function"?t(e):t}function oi(e){var t=at(),n=t.queue;if(n===null)throw Error(u(311));n.lastRenderedReducer=e;var r=Me,l=r.baseQueue,o=n.pending;if(o!==null){if(l!==null){var i=l.next;l.next=o.next,o.next=i}r.baseQueue=l=o,n.pending=null}if(l!==null){o=l.next,r=r.baseState;var a=i=null,c=null,y=o;do{var _=y.lane;if((fn&_)===_)c!==null&&(c=c.next={lane:0,action:y.action,hasEagerState:y.hasEagerState,eagerState:y.eagerState,next:null}),r=y.hasEagerState?y.eagerState:e(r,y.action);else{var z={lane:_,action:y.action,hasEagerState:y.hasEagerState,eagerState:y.eagerState,next:null};c===null?(a=c=z,i=r):c=c.next=z,Ne.lanes|=_,pn|=_}y=y.next}while(y!==null&&y!==o);c===null?i=r:c.next=a,pt(r,t.memoizedState)||(Ze=!0),t.memoizedState=r,t.baseState=i,t.baseQueue=c,n.lastRenderedState=r}if(e=n.interleaved,e!==null){l=e;do o=l.lane,Ne.lanes|=o,pn|=o,l=l.next;while(l!==e)}else l===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function ii(e){var t=at(),n=t.queue;if(n===null)throw Error(u(311));n.lastRenderedReducer=e;var r=n.dispatch,l=n.pending,o=t.memoizedState;if(l!==null){n.pending=null;var i=l=l.next;do o=e(o,i.action),i=i.next;while(i!==l);pt(o,t.memoizedState)||(Ze=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function Ru(){}function Iu(e,t){var n=Ne,r=at(),l=t(),o=!pt(r.memoizedState,l);if(o&&(r.memoizedState=l,Ze=!0),r=r.queue,si(Mu.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||De!==null&&De.memoizedState.tag&1){if(n.flags|=2048,_r(9,Lu.bind(null,n,r,l,t),void 0,null),Fe===null)throw Error(u(349));(fn&30)!==0||Tu(n,t,l)}return l}function Tu(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Ne.updateQueue,t===null?(t={lastEffect:null,stores:null},Ne.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Lu(e,t,n,r){t.value=n,t.getSnapshot=r,Ou(t)&&Au(e)}function Mu(e,t,n){return n(function(){Ou(t)&&Au(e)})}function Ou(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!pt(e,n)}catch{return!0}}function Au(e){var t=jt(e,1);t!==null&&yt(t,e,1,-1)}function Du(e){var t=Ct();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Er,lastRenderedState:e},t.queue=e,e=e.dispatch=Kd.bind(null,Ne,e),[t.memoizedState,e]}function _r(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=Ne.updateQueue,t===null?(t={lastEffect:null,stores:null},Ne.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Fu(){return at().memoizedState}function xl(e,t,n,r){var l=Ct();Ne.flags|=e,l.memoizedState=_r(1|t,n,void 0,r===void 0?null:r)}function Sl(e,t,n,r){var l=at();r=r===void 0?null:r;var o=void 0;if(Me!==null){var i=Me.memoizedState;if(o=i.destroy,r!==null&&ni(r,i.deps)){l.memoizedState=_r(t,n,o,r);return}}Ne.flags|=e,l.memoizedState=_r(1|t,n,o,r)}function Uu(e,t){return xl(8390656,8,e,t)}function si(e,t){return Sl(2048,8,e,t)}function Vu(e,t){return Sl(4,2,e,t)}function $u(e,t){return Sl(4,4,e,t)}function Wu(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Bu(e,t,n){return n=n!=null?n.concat([e]):null,Sl(4,4,Wu.bind(null,t,e),n)}function ui(){}function Hu(e,t){var n=at();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&ni(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Qu(e,t){var n=at();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&ni(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Ku(e,t,n){return(fn&21)===0?(e.baseState&&(e.baseState=!1,Ze=!0),e.memoizedState=n):(pt(n,t)||(n=ks(),Ne.lanes|=n,pn|=n,e.baseState=!0),t)}function Hd(e,t){var n=pe;pe=n!==0&&4>n?n:4,e(!0);var r=ti.transition;ti.transition={};try{e(!1),t()}finally{pe=n,ti.transition=r}}function Gu(){return at().memoizedState}function Qd(e,t,n){var r=Zt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Yu(e))Xu(t,n);else if(n=_u(e,t,n,r),n!==null){var l=Ge();yt(n,e,r,l),Ju(n,t,r)}}function Kd(e,t,n){var r=Zt(e),l={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Yu(e))Xu(t,l);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var i=t.lastRenderedState,a=o(i,n);if(l.hasEagerState=!0,l.eagerState=a,pt(a,i)){var c=t.interleaved;c===null?(l.next=l,Xo(t)):(l.next=c.next,c.next=l),t.interleaved=l;return}}catch{}finally{}n=_u(e,t,l,r),n!==null&&(l=Ge(),yt(n,e,r,l),Ju(n,t,r))}}function Yu(e){var t=e.alternate;return e===Ne||t!==null&&t===Ne}function Xu(e,t){kr=wl=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Ju(e,t,n){if((n&4194240)!==0){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,ao(e,n)}}var kl={readContext:ut,useCallback:Be,useContext:Be,useEffect:Be,useImperativeHandle:Be,useInsertionEffect:Be,useLayoutEffect:Be,useMemo:Be,useReducer:Be,useRef:Be,useState:Be,useDebugValue:Be,useDeferredValue:Be,useTransition:Be,useMutableSource:Be,useSyncExternalStore:Be,useId:Be,unstable_isNewReconciler:!1},Gd={readContext:ut,useCallback:function(e,t){return Ct().memoizedState=[e,t===void 0?null:t],e},useContext:ut,useEffect:Uu,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,xl(4194308,4,Wu.bind(null,t,e),n)},useLayoutEffect:function(e,t){return xl(4194308,4,e,t)},useInsertionEffect:function(e,t){return xl(4,2,e,t)},useMemo:function(e,t){var n=Ct();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Ct();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Qd.bind(null,Ne,e),[r.memoizedState,e]},useRef:function(e){var t=Ct();return e={current:e},t.memoizedState=e},useState:Du,useDebugValue:ui,useDeferredValue:function(e){return Ct().memoizedState=e},useTransition:function(){var e=Du(!1),t=e[0];return e=Hd.bind(null,e[1]),Ct().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=Ne,l=Ct();if(ke){if(n===void 0)throw Error(u(407));n=n()}else{if(n=t(),Fe===null)throw Error(u(349));(fn&30)!==0||Tu(r,t,n)}l.memoizedState=n;var o={value:n,getSnapshot:t};return l.queue=o,Uu(Mu.bind(null,r,o,e),[e]),r.flags|=2048,_r(9,Lu.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=Ct(),t=Fe.identifierPrefix;if(ke){var n=zt,r=Pt;n=(r&~(1<<32-ft(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Cr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Bd++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Yd={readContext:ut,useCallback:Hu,useContext:ut,useEffect:si,useImperativeHandle:Bu,useInsertionEffect:Vu,useLayoutEffect:$u,useMemo:Qu,useReducer:oi,useRef:Fu,useState:function(){return oi(Er)},useDebugValue:ui,useDeferredValue:function(e){var t=at();return Ku(t,Me.memoizedState,e)},useTransition:function(){var e=oi(Er)[0],t=at().memoizedState;return[e,t]},useMutableSource:Ru,useSyncExternalStore:Iu,useId:Gu,unstable_isNewReconciler:!1},Xd={readContext:ut,useCallback:Hu,useContext:ut,useEffect:si,useImperativeHandle:Bu,useInsertionEffect:Vu,useLayoutEffect:$u,useMemo:Qu,useReducer:ii,useRef:Fu,useState:function(){return ii(Er)},useDebugValue:ui,useDeferredValue:function(e){var t=at();return Me===null?t.memoizedState=e:Ku(t,Me.memoizedState,e)},useTransition:function(){var e=ii(Er)[0],t=at().memoizedState;return[e,t]},useMutableSource:Ru,useSyncExternalStore:Iu,useId:Gu,unstable_isNewReconciler:!1};function ht(e,t){if(e&&e.defaultProps){t=R({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function ai(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:R({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Cl={isMounted:function(e){return(e=e._reactInternals)?ln(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Ge(),l=Zt(e),o=Rt(r,l);o.payload=t,n!=null&&(o.callback=n),t=Gt(e,o,l),t!==null&&(yt(t,e,l,r),hl(t,e,l))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Ge(),l=Zt(e),o=Rt(r,l);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=Gt(e,o,l),t!==null&&(yt(t,e,l,r),hl(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Ge(),r=Zt(e),l=Rt(n,r);l.tag=2,t!=null&&(l.callback=t),t=Gt(e,l,r),t!==null&&(yt(t,e,r,n),hl(t,e,r))}};function Zu(e,t,n,r,l,o,i){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,o,i):t.prototype&&t.prototype.isPureReactComponent?!dr(n,r)||!dr(l,o):!0}function qu(e,t,n){var r=!1,l=Ht,o=t.contextType;return typeof o=="object"&&o!==null?o=ut(o):(l=Je(t)?sn:We.current,r=t.contextTypes,o=(r=r!=null)?In(e,l):Ht),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Cl,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=l,e.__reactInternalMemoizedMaskedChildContext=o),t}function bu(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Cl.enqueueReplaceState(t,t.state,null)}function ci(e,t,n,r){var l=e.stateNode;l.props=n,l.state=e.memoizedState,l.refs={},Jo(e);var o=t.contextType;typeof o=="object"&&o!==null?l.context=ut(o):(o=Je(t)?sn:We.current,l.context=In(e,o)),l.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(ai(e,t,o,n),l.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof l.getSnapshotBeforeUpdate=="function"||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(t=l.state,typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount(),t!==l.state&&Cl.enqueueReplaceState(l,l.state,null),gl(e,n,l,r),l.state=e.memoizedState),typeof l.componentDidMount=="function"&&(e.flags|=4194308)}function Un(e,t){try{var n="",r=t;do n+=le(r),r=r.return;while(r);var l=n}catch(o){l=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:l,digest:null}}function di(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function fi(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Jd=typeof WeakMap=="function"?WeakMap:Map;function ea(e,t,n){n=Rt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Rl||(Rl=!0,Pi=r),fi(e,t)},n}function ta(e,t,n){n=Rt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var l=t.value;n.payload=function(){return r(l)},n.callback=function(){fi(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){fi(e,t),typeof r!="function"&&(Xt===null?Xt=new Set([this]):Xt.add(this));var i=t.stack;this.componentDidCatch(t.value,{componentStack:i!==null?i:""})}),n}function na(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Jd;var l=new Set;r.set(t,l)}else l=r.get(t),l===void 0&&(l=new Set,r.set(t,l));l.has(n)||(l.add(n),e=df.bind(null,e,t,n),t.then(e,e))}function ra(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function la(e,t,n,r,l){return(e.mode&1)===0?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Rt(-1,1),t.tag=2,Gt(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=l,e)}var Zd=ue.ReactCurrentOwner,Ze=!1;function Ke(e,t,n,r){t.child=e===null?Eu(t,null,n,r):On(t,e.child,n,r)}function oa(e,t,n,r,l){n=n.render;var o=t.ref;return Dn(t,l),r=ri(e,t,n,r,o,l),n=li(),e!==null&&!Ze?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,It(e,t,l)):(ke&&n&&Vo(t),t.flags|=1,Ke(e,t,r,l),t.child)}function ia(e,t,n,r,l){if(e===null){var o=n.type;return typeof o=="function"&&!Mi(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,sa(e,t,o,r,l)):(e=Al(n.type,null,r,t,t.mode,l),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,(e.lanes&l)===0){var i=o.memoizedProps;if(n=n.compare,n=n!==null?n:dr,n(i,r)&&e.ref===t.ref)return It(e,t,l)}return t.flags|=1,e=bt(o,r),e.ref=t.ref,e.return=t,t.child=e}function sa(e,t,n,r,l){if(e!==null){var o=e.memoizedProps;if(dr(o,r)&&e.ref===t.ref)if(Ze=!1,t.pendingProps=r=o,(e.lanes&l)!==0)(e.flags&131072)!==0&&(Ze=!0);else return t.lanes=e.lanes,It(e,t,l)}return pi(e,t,n,r,l)}function ua(e,t,n){var r=t.pendingProps,l=r.children,o=e!==null?e.memoizedState:null;if(r.mode==="hidden")if((t.mode&1)===0)t.memoizedState={baseLanes:0,cachePool:null,transitions:null},ve($n,lt),lt|=n;else{if((n&1073741824)===0)return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,ve($n,lt),lt|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=o!==null?o.baseLanes:n,ve($n,lt),lt|=r}else o!==null?(r=o.baseLanes|n,t.memoizedState=null):r=n,ve($n,lt),lt|=r;return Ke(e,t,l,n),t.child}function aa(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function pi(e,t,n,r,l){var o=Je(n)?sn:We.current;return o=In(t,o),Dn(t,l),n=ri(e,t,n,r,o,l),r=li(),e!==null&&!Ze?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,It(e,t,l)):(ke&&r&&Vo(t),t.flags|=1,Ke(e,t,n,l),t.child)}function ca(e,t,n,r,l){if(Je(n)){var o=!0;sl(t)}else o=!1;if(Dn(t,l),t.stateNode===null)_l(e,t),qu(t,n,r),ci(t,n,r,l),r=!0;else if(e===null){var i=t.stateNode,a=t.memoizedProps;i.props=a;var c=i.context,y=n.contextType;typeof y=="object"&&y!==null?y=ut(y):(y=Je(n)?sn:We.current,y=In(t,y));var _=n.getDerivedStateFromProps,z=typeof _=="function"||typeof i.getSnapshotBeforeUpdate=="function";z||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(a!==r||c!==y)&&bu(t,i,r,y),Kt=!1;var E=t.memoizedState;i.state=E,gl(t,r,i,l),c=t.memoizedState,a!==r||E!==c||Xe.current||Kt?(typeof _=="function"&&(ai(t,n,_,r),c=t.memoizedState),(a=Kt||Zu(t,n,a,r,E,c,y))?(z||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(t.flags|=4194308)):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=c),i.props=r,i.state=c,i.context=y,r=a):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,Nu(e,t),a=t.memoizedProps,y=t.type===t.elementType?a:ht(t.type,a),i.props=y,z=t.pendingProps,E=i.context,c=n.contextType,typeof c=="object"&&c!==null?c=ut(c):(c=Je(n)?sn:We.current,c=In(t,c));var T=n.getDerivedStateFromProps;(_=typeof T=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(a!==z||E!==c)&&bu(t,i,r,c),Kt=!1,E=t.memoizedState,i.state=E,gl(t,r,i,l);var O=t.memoizedState;a!==z||E!==O||Xe.current||Kt?(typeof T=="function"&&(ai(t,n,T,r),O=t.memoizedState),(y=Kt||Zu(t,n,y,r,E,O,c)||!1)?(_||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(r,O,c),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(r,O,c)),typeof i.componentDidUpdate=="function"&&(t.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof i.componentDidUpdate!="function"||a===e.memoizedProps&&E===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&E===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=O),i.props=r,i.state=O,i.context=c,r=y):(typeof i.componentDidUpdate!="function"||a===e.memoizedProps&&E===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&E===e.memoizedState||(t.flags|=1024),r=!1)}return mi(e,t,n,r,o,l)}function mi(e,t,n,r,l,o){aa(e,t);var i=(t.flags&128)!==0;if(!r&&!i)return l&&hu(t,n,!1),It(e,t,o);r=t.stateNode,Zd.current=t;var a=i&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&i?(t.child=On(t,e.child,null,o),t.child=On(t,null,a,o)):Ke(e,t,a,o),t.memoizedState=r.state,l&&hu(t,n,!0),t.child}function da(e){var t=e.stateNode;t.pendingContext?pu(e,t.pendingContext,t.pendingContext!==t.context):t.context&&pu(e,t.context,!1),Zo(e,t.containerInfo)}function fa(e,t,n,r,l){return Mn(),Ho(l),t.flags|=256,Ke(e,t,n,r),t.child}var hi={dehydrated:null,treeContext:null,retryLane:0};function gi(e){return{baseLanes:e,cachePool:null,transitions:null}}function pa(e,t,n){var r=t.pendingProps,l=_e.current,o=!1,i=(t.flags&128)!==0,a;if((a=i)||(a=e!==null&&e.memoizedState===null?!1:(l&2)!==0),a?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(l|=1),ve(_e,l&1),e===null)return Bo(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?((t.mode&1)===0?t.lanes=1:e.data==="$!"?t.lanes=8:t.lanes=1073741824,null):(i=r.children,e=r.fallback,o?(r=t.mode,o=t.child,i={mode:"hidden",children:i},(r&1)===0&&o!==null?(o.childLanes=0,o.pendingProps=i):o=Dl(i,r,0,null),e=vn(e,r,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=gi(n),t.memoizedState=hi,e):vi(t,i));if(l=e.memoizedState,l!==null&&(a=l.dehydrated,a!==null))return qd(e,t,i,r,a,l,n);if(o){o=r.fallback,i=t.mode,l=e.child,a=l.sibling;var c={mode:"hidden",children:r.children};return(i&1)===0&&t.child!==l?(r=t.child,r.childLanes=0,r.pendingProps=c,t.deletions=null):(r=bt(l,c),r.subtreeFlags=l.subtreeFlags&14680064),a!==null?o=bt(a,o):(o=vn(o,i,n,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,i=e.child.memoizedState,i=i===null?gi(n):{baseLanes:i.baseLanes|n,cachePool:null,transitions:i.transitions},o.memoizedState=i,o.childLanes=e.childLanes&~n,t.memoizedState=hi,r}return o=e.child,e=o.sibling,r=bt(o,{mode:"visible",children:r.children}),(t.mode&1)===0&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function vi(e,t){return t=Dl({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function El(e,t,n,r){return r!==null&&Ho(r),On(t,e.child,null,n),e=vi(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function qd(e,t,n,r,l,o,i){if(n)return t.flags&256?(t.flags&=-257,r=di(Error(u(422))),El(e,t,i,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=r.fallback,l=t.mode,r=Dl({mode:"visible",children:r.children},l,0,null),o=vn(o,l,i,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,(t.mode&1)!==0&&On(t,e.child,null,i),t.child.memoizedState=gi(i),t.memoizedState=hi,o);if((t.mode&1)===0)return El(e,t,i,null);if(l.data==="$!"){if(r=l.nextSibling&&l.nextSibling.dataset,r)var a=r.dgst;return r=a,o=Error(u(419)),r=di(o,r,void 0),El(e,t,i,r)}if(a=(i&e.childLanes)!==0,Ze||a){if(r=Fe,r!==null){switch(i&-i){case 4:l=2;break;case 16:l=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:l=32;break;case 536870912:l=268435456;break;default:l=0}l=(l&(r.suspendedLanes|i))!==0?0:l,l!==0&&l!==o.retryLane&&(o.retryLane=l,jt(e,l),yt(r,e,l,-1))}return Li(),r=di(Error(u(421))),El(e,t,i,r)}return l.data==="$?"?(t.flags|=128,t.child=e.child,t=ff.bind(null,e),l._reactRetry=t,null):(e=o.treeContext,rt=Wt(l.nextSibling),nt=t,ke=!0,mt=null,e!==null&&(it[st++]=Pt,it[st++]=zt,it[st++]=un,Pt=e.id,zt=e.overflow,un=t),t=vi(t,r.children),t.flags|=4096,t)}function ma(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Yo(e.return,t,n)}function yi(e,t,n,r,l){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:l}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=l)}function ha(e,t,n){var r=t.pendingProps,l=r.revealOrder,o=r.tail;if(Ke(e,t,r.children,n),r=_e.current,(r&2)!==0)r=r&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&ma(e,n,t);else if(e.tag===19)ma(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(ve(_e,r),(t.mode&1)===0)t.memoizedState=null;else switch(l){case"forwards":for(n=t.child,l=null;n!==null;)e=n.alternate,e!==null&&vl(e)===null&&(l=n),n=n.sibling;n=l,n===null?(l=t.child,t.child=null):(l=n.sibling,n.sibling=null),yi(t,!1,l,n,o);break;case"backwards":for(n=null,l=t.child,t.child=null;l!==null;){if(e=l.alternate,e!==null&&vl(e)===null){t.child=l;break}e=l.sibling,l.sibling=n,n=l,l=e}yi(t,!0,n,null,o);break;case"together":yi(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function _l(e,t){(t.mode&1)===0&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function It(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),pn|=t.lanes,(n&t.childLanes)===0)return null;if(e!==null&&t.child!==e.child)throw Error(u(153));if(t.child!==null){for(e=t.child,n=bt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=bt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function bd(e,t,n){switch(t.tag){case 3:da(t),Mn();break;case 5:ju(t);break;case 1:Je(t.type)&&sl(t);break;case 4:Zo(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,l=t.memoizedProps.value;ve(pl,r._currentValue),r._currentValue=l;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(ve(_e,_e.current&1),t.flags|=128,null):(n&t.child.childLanes)!==0?pa(e,t,n):(ve(_e,_e.current&1),e=It(e,t,n),e!==null?e.sibling:null);ve(_e,_e.current&1);break;case 19:if(r=(n&t.childLanes)!==0,(e.flags&128)!==0){if(r)return ha(e,t,n);t.flags|=128}if(l=t.memoizedState,l!==null&&(l.rendering=null,l.tail=null,l.lastEffect=null),ve(_e,_e.current),r)break;return null;case 22:case 23:return t.lanes=0,ua(e,t,n)}return It(e,t,n)}var ga,wi,va,ya;ga=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},wi=function(){},va=function(e,t,n,r){var l=e.memoizedProps;if(l!==r){e=t.stateNode,dn(kt.current);var o=null;switch(n){case"input":l=Gl(e,l),r=Gl(e,r),o=[];break;case"select":l=R({},l,{value:void 0}),r=R({},r,{value:void 0}),o=[];break;case"textarea":l=Jl(e,l),r=Jl(e,r),o=[];break;default:typeof l.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=ll)}ql(n,r);var i;n=null;for(y in l)if(!r.hasOwnProperty(y)&&l.hasOwnProperty(y)&&l[y]!=null)if(y==="style"){var a=l[y];for(i in a)a.hasOwnProperty(i)&&(n||(n={}),n[i]="")}else y!=="dangerouslySetInnerHTML"&&y!=="children"&&y!=="suppressContentEditableWarning"&&y!=="suppressHydrationWarning"&&y!=="autoFocus"&&(g.hasOwnProperty(y)?o||(o=[]):(o=o||[]).push(y,null));for(y in r){var c=r[y];if(a=l!=null?l[y]:void 0,r.hasOwnProperty(y)&&c!==a&&(c!=null||a!=null))if(y==="style")if(a){for(i in a)!a.hasOwnProperty(i)||c&&c.hasOwnProperty(i)||(n||(n={}),n[i]="");for(i in c)c.hasOwnProperty(i)&&a[i]!==c[i]&&(n||(n={}),n[i]=c[i])}else n||(o||(o=[]),o.push(y,n)),n=c;else y==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,a=a?a.__html:void 0,c!=null&&a!==c&&(o=o||[]).push(y,c)):y==="children"?typeof c!="string"&&typeof c!="number"||(o=o||[]).push(y,""+c):y!=="suppressContentEditableWarning"&&y!=="suppressHydrationWarning"&&(g.hasOwnProperty(y)?(c!=null&&y==="onScroll"&&we("scroll",e),o||a===c||(o=[])):(o=o||[]).push(y,c))}n&&(o=o||[]).push("style",n);var y=o;(t.updateQueue=y)&&(t.flags|=4)}},ya=function(e,t,n,r){n!==r&&(t.flags|=4)};function Nr(e,t){if(!ke)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function He(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var l=e.child;l!==null;)n|=l.lanes|l.childLanes,r|=l.subtreeFlags&14680064,r|=l.flags&14680064,l.return=e,l=l.sibling;else for(l=e.child;l!==null;)n|=l.lanes|l.childLanes,r|=l.subtreeFlags,r|=l.flags,l.return=e,l=l.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function ef(e,t,n){var r=t.pendingProps;switch($o(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return He(t),null;case 1:return Je(t.type)&&il(),He(t),null;case 3:return r=t.stateNode,Fn(),xe(Xe),xe(We),ei(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(dl(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,mt!==null&&(Ri(mt),mt=null))),wi(e,t),He(t),null;case 5:qo(t);var l=dn(Sr.current);if(n=t.type,e!==null&&t.stateNode!=null)va(e,t,n,r,l),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(u(166));return He(t),null}if(e=dn(kt.current),dl(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[St]=t,r[gr]=o,e=(t.mode&1)!==0,n){case"dialog":we("cancel",r),we("close",r);break;case"iframe":case"object":case"embed":we("load",r);break;case"video":case"audio":for(l=0;l<pr.length;l++)we(pr[l],r);break;case"source":we("error",r);break;case"img":case"image":case"link":we("error",r),we("load",r);break;case"details":we("toggle",r);break;case"input":qi(r,o),we("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},we("invalid",r);break;case"textarea":ts(r,o),we("invalid",r)}ql(n,o),l=null;for(var i in o)if(o.hasOwnProperty(i)){var a=o[i];i==="children"?typeof a=="string"?r.textContent!==a&&(o.suppressHydrationWarning!==!0&&rl(r.textContent,a,e),l=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(o.suppressHydrationWarning!==!0&&rl(r.textContent,a,e),l=["children",""+a]):g.hasOwnProperty(i)&&a!=null&&i==="onScroll"&&we("scroll",r)}switch(n){case"input":yn(r),es(r,o,!0);break;case"textarea":yn(r),rs(r);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(r.onclick=ll)}r=l,t.updateQueue=r,r!==null&&(t.flags|=4)}else{i=l.nodeType===9?l:l.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=ls(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=i.createElement(n,{is:r.is}):(e=i.createElement(n),n==="select"&&(i=e,r.multiple?i.multiple=!0:r.size&&(i.size=r.size))):e=i.createElementNS(e,n),e[St]=t,e[gr]=r,ga(e,t,!1,!1),t.stateNode=e;e:{switch(i=bl(n,r),n){case"dialog":we("cancel",e),we("close",e),l=r;break;case"iframe":case"object":case"embed":we("load",e),l=r;break;case"video":case"audio":for(l=0;l<pr.length;l++)we(pr[l],e);l=r;break;case"source":we("error",e),l=r;break;case"img":case"image":case"link":we("error",e),we("load",e),l=r;break;case"details":we("toggle",e),l=r;break;case"input":qi(e,r),l=Gl(e,r),we("invalid",e);break;case"option":l=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},l=R({},r,{value:void 0}),we("invalid",e);break;case"textarea":ts(e,r),l=Jl(e,r),we("invalid",e);break;default:l=r}ql(n,l),a=l;for(o in a)if(a.hasOwnProperty(o)){var c=a[o];o==="style"?ss(e,c):o==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,c!=null&&os(e,c)):o==="children"?typeof c=="string"?(n!=="textarea"||c!=="")&&Yn(e,c):typeof c=="number"&&Yn(e,""+c):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(g.hasOwnProperty(o)?c!=null&&o==="onScroll"&&we("scroll",e):c!=null&&Ce(e,o,c,i))}switch(n){case"input":yn(e),es(e,r,!1);break;case"textarea":yn(e),rs(e);break;case"option":r.value!=null&&e.setAttribute("value",""+de(r.value));break;case"select":e.multiple=!!r.multiple,o=r.value,o!=null?wn(e,!!r.multiple,o,!1):r.defaultValue!=null&&wn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof l.onClick=="function"&&(e.onclick=ll)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return He(t),null;case 6:if(e&&t.stateNode!=null)ya(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(u(166));if(n=dn(Sr.current),dn(kt.current),dl(t)){if(r=t.stateNode,n=t.memoizedProps,r[St]=t,(o=r.nodeValue!==n)&&(e=nt,e!==null))switch(e.tag){case 3:rl(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&rl(r.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[St]=t,t.stateNode=r}return He(t),null;case 13:if(xe(_e),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(ke&&rt!==null&&(t.mode&1)!==0&&(t.flags&128)===0)Su(),Mn(),t.flags|=98560,o=!1;else if(o=dl(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(u(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(u(317));o[St]=t}else Mn(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;He(t),o=!1}else mt!==null&&(Ri(mt),mt=null),o=!0;if(!o)return t.flags&65536?t:null}return(t.flags&128)!==0?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,(t.mode&1)!==0&&(e===null||(_e.current&1)!==0?Oe===0&&(Oe=3):Li())),t.updateQueue!==null&&(t.flags|=4),He(t),null);case 4:return Fn(),wi(e,t),e===null&&mr(t.stateNode.containerInfo),He(t),null;case 10:return Go(t.type._context),He(t),null;case 17:return Je(t.type)&&il(),He(t),null;case 19:if(xe(_e),o=t.memoizedState,o===null)return He(t),null;if(r=(t.flags&128)!==0,i=o.rendering,i===null)if(r)Nr(o,!1);else{if(Oe!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(i=vl(e),i!==null){for(t.flags|=128,Nr(o,!1),r=i.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)o=n,e=r,o.flags&=14680066,i=o.alternate,i===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=i.childLanes,o.lanes=i.lanes,o.child=i.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=i.memoizedProps,o.memoizedState=i.memoizedState,o.updateQueue=i.updateQueue,o.type=i.type,e=i.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return ve(_e,_e.current&1|2),t.child}e=e.sibling}o.tail!==null&&je()>Wn&&(t.flags|=128,r=!0,Nr(o,!1),t.lanes=4194304)}else{if(!r)if(e=vl(i),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Nr(o,!0),o.tail===null&&o.tailMode==="hidden"&&!i.alternate&&!ke)return He(t),null}else 2*je()-o.renderingStartTime>Wn&&n!==1073741824&&(t.flags|=128,r=!0,Nr(o,!1),t.lanes=4194304);o.isBackwards?(i.sibling=t.child,t.child=i):(n=o.last,n!==null?n.sibling=i:t.child=i,o.last=i)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=je(),t.sibling=null,n=_e.current,ve(_e,r?n&1|2:n&1),t):(He(t),null);case 22:case 23:return Ti(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&(t.mode&1)!==0?(lt&1073741824)!==0&&(He(t),t.subtreeFlags&6&&(t.flags|=8192)):He(t),null;case 24:return null;case 25:return null}throw Error(u(156,t.tag))}function tf(e,t){switch($o(t),t.tag){case 1:return Je(t.type)&&il(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Fn(),xe(Xe),xe(We),ei(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 5:return qo(t),null;case 13:if(xe(_e),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(u(340));Mn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return xe(_e),null;case 4:return Fn(),null;case 10:return Go(t.type._context),null;case 22:case 23:return Ti(),null;case 24:return null;default:return null}}var Nl=!1,Qe=!1,nf=typeof WeakSet=="function"?WeakSet:Set,M=null;function Vn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Pe(e,t,r)}else n.current=null}function xi(e,t,n){try{n()}catch(r){Pe(e,t,r)}}var wa=!1;function rf(e,t){if(To=Kr,e=Zs(),Eo(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var l=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var i=0,a=-1,c=-1,y=0,_=0,z=e,E=null;t:for(;;){for(var T;z!==n||l!==0&&z.nodeType!==3||(a=i+l),z!==o||r!==0&&z.nodeType!==3||(c=i+r),z.nodeType===3&&(i+=z.nodeValue.length),(T=z.firstChild)!==null;)E=z,z=T;for(;;){if(z===e)break t;if(E===n&&++y===l&&(a=i),E===o&&++_===r&&(c=i),(T=z.nextSibling)!==null)break;z=E,E=z.parentNode}z=T}n=a===-1||c===-1?null:{start:a,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(Lo={focusedElem:e,selectionRange:n},Kr=!1,M=t;M!==null;)if(t=M,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,M=e;else for(;M!==null;){t=M;try{var O=t.alternate;if((t.flags&1024)!==0)switch(t.tag){case 0:case 11:case 15:break;case 1:if(O!==null){var U=O.memoizedProps,Re=O.memoizedState,m=t.stateNode,f=m.getSnapshotBeforeUpdate(t.elementType===t.type?U:ht(t.type,U),Re);m.__reactInternalSnapshotBeforeUpdate=f}break;case 3:var h=t.stateNode.containerInfo;h.nodeType===1?h.textContent="":h.nodeType===9&&h.documentElement&&h.removeChild(h.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(u(163))}}catch(j){Pe(t,t.return,j)}if(e=t.sibling,e!==null){e.return=t.return,M=e;break}M=t.return}return O=wa,wa=!1,O}function Pr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var l=r=r.next;do{if((l.tag&e)===e){var o=l.destroy;l.destroy=void 0,o!==void 0&&xi(t,n,o)}l=l.next}while(l!==r)}}function Pl(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Si(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function xa(e){var t=e.alternate;t!==null&&(e.alternate=null,xa(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[St],delete t[gr],delete t[Do],delete t[Ud],delete t[Vd])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Sa(e){return e.tag===5||e.tag===3||e.tag===4}function ka(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Sa(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function ki(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=ll));else if(r!==4&&(e=e.child,e!==null))for(ki(e,t,n),e=e.sibling;e!==null;)ki(e,t,n),e=e.sibling}function Ci(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Ci(e,t,n),e=e.sibling;e!==null;)Ci(e,t,n),e=e.sibling}var Ve=null,gt=!1;function Yt(e,t,n){for(n=n.child;n!==null;)Ca(e,t,n),n=n.sibling}function Ca(e,t,n){if(xt&&typeof xt.onCommitFiberUnmount=="function")try{xt.onCommitFiberUnmount(Vr,n)}catch{}switch(n.tag){case 5:Qe||Vn(n,t);case 6:var r=Ve,l=gt;Ve=null,Yt(e,t,n),Ve=r,gt=l,Ve!==null&&(gt?(e=Ve,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Ve.removeChild(n.stateNode));break;case 18:Ve!==null&&(gt?(e=Ve,n=n.stateNode,e.nodeType===8?Ao(e.parentNode,n):e.nodeType===1&&Ao(e,n),or(e)):Ao(Ve,n.stateNode));break;case 4:r=Ve,l=gt,Ve=n.stateNode.containerInfo,gt=!0,Yt(e,t,n),Ve=r,gt=l;break;case 0:case 11:case 14:case 15:if(!Qe&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){l=r=r.next;do{var o=l,i=o.destroy;o=o.tag,i!==void 0&&((o&2)!==0||(o&4)!==0)&&xi(n,t,i),l=l.next}while(l!==r)}Yt(e,t,n);break;case 1:if(!Qe&&(Vn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){Pe(n,t,a)}Yt(e,t,n);break;case 21:Yt(e,t,n);break;case 22:n.mode&1?(Qe=(r=Qe)||n.memoizedState!==null,Yt(e,t,n),Qe=r):Yt(e,t,n);break;default:Yt(e,t,n)}}function Ea(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new nf),t.forEach(function(r){var l=pf.bind(null,e,r);n.has(r)||(n.add(r),r.then(l,l))})}}function vt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var l=n[r];try{var o=e,i=t,a=i;e:for(;a!==null;){switch(a.tag){case 5:Ve=a.stateNode,gt=!1;break e;case 3:Ve=a.stateNode.containerInfo,gt=!0;break e;case 4:Ve=a.stateNode.containerInfo,gt=!0;break e}a=a.return}if(Ve===null)throw Error(u(160));Ca(o,i,l),Ve=null,gt=!1;var c=l.alternate;c!==null&&(c.return=null),l.return=null}catch(y){Pe(l,t,y)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)_a(t,e),t=t.sibling}function _a(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(vt(t,e),Et(e),r&4){try{Pr(3,e,e.return),Pl(3,e)}catch(U){Pe(e,e.return,U)}try{Pr(5,e,e.return)}catch(U){Pe(e,e.return,U)}}break;case 1:vt(t,e),Et(e),r&512&&n!==null&&Vn(n,n.return);break;case 5:if(vt(t,e),Et(e),r&512&&n!==null&&Vn(n,n.return),e.flags&32){var l=e.stateNode;try{Yn(l,"")}catch(U){Pe(e,e.return,U)}}if(r&4&&(l=e.stateNode,l!=null)){var o=e.memoizedProps,i=n!==null?n.memoizedProps:o,a=e.type,c=e.updateQueue;if(e.updateQueue=null,c!==null)try{a==="input"&&o.type==="radio"&&o.name!=null&&bi(l,o),bl(a,i);var y=bl(a,o);for(i=0;i<c.length;i+=2){var _=c[i],z=c[i+1];_==="style"?ss(l,z):_==="dangerouslySetInnerHTML"?os(l,z):_==="children"?Yn(l,z):Ce(l,_,z,y)}switch(a){case"input":Yl(l,o);break;case"textarea":ns(l,o);break;case"select":var E=l._wrapperState.wasMultiple;l._wrapperState.wasMultiple=!!o.multiple;var T=o.value;T!=null?wn(l,!!o.multiple,T,!1):E!==!!o.multiple&&(o.defaultValue!=null?wn(l,!!o.multiple,o.defaultValue,!0):wn(l,!!o.multiple,o.multiple?[]:"",!1))}l[gr]=o}catch(U){Pe(e,e.return,U)}}break;case 6:if(vt(t,e),Et(e),r&4){if(e.stateNode===null)throw Error(u(162));l=e.stateNode,o=e.memoizedProps;try{l.nodeValue=o}catch(U){Pe(e,e.return,U)}}break;case 3:if(vt(t,e),Et(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{or(t.containerInfo)}catch(U){Pe(e,e.return,U)}break;case 4:vt(t,e),Et(e);break;case 13:vt(t,e),Et(e),l=e.child,l.flags&8192&&(o=l.memoizedState!==null,l.stateNode.isHidden=o,!o||l.alternate!==null&&l.alternate.memoizedState!==null||(Ni=je())),r&4&&Ea(e);break;case 22:if(_=n!==null&&n.memoizedState!==null,e.mode&1?(Qe=(y=Qe)||_,vt(t,e),Qe=y):vt(t,e),Et(e),r&8192){if(y=e.memoizedState!==null,(e.stateNode.isHidden=y)&&!_&&(e.mode&1)!==0)for(M=e,_=e.child;_!==null;){for(z=M=_;M!==null;){switch(E=M,T=E.child,E.tag){case 0:case 11:case 14:case 15:Pr(4,E,E.return);break;case 1:Vn(E,E.return);var O=E.stateNode;if(typeof O.componentWillUnmount=="function"){r=E,n=E.return;try{t=r,O.props=t.memoizedProps,O.state=t.memoizedState,O.componentWillUnmount()}catch(U){Pe(r,n,U)}}break;case 5:Vn(E,E.return);break;case 22:if(E.memoizedState!==null){za(z);continue}}T!==null?(T.return=E,M=T):za(z)}_=_.sibling}e:for(_=null,z=e;;){if(z.tag===5){if(_===null){_=z;try{l=z.stateNode,y?(o=l.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(a=z.stateNode,c=z.memoizedProps.style,i=c!=null&&c.hasOwnProperty("display")?c.display:null,a.style.display=is("display",i))}catch(U){Pe(e,e.return,U)}}}else if(z.tag===6){if(_===null)try{z.stateNode.nodeValue=y?"":z.memoizedProps}catch(U){Pe(e,e.return,U)}}else if((z.tag!==22&&z.tag!==23||z.memoizedState===null||z===e)&&z.child!==null){z.child.return=z,z=z.child;continue}if(z===e)break e;for(;z.sibling===null;){if(z.return===null||z.return===e)break e;_===z&&(_=null),z=z.return}_===z&&(_=null),z.sibling.return=z.return,z=z.sibling}}break;case 19:vt(t,e),Et(e),r&4&&Ea(e);break;case 21:break;default:vt(t,e),Et(e)}}function Et(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Sa(n)){var r=n;break e}n=n.return}throw Error(u(160))}switch(r.tag){case 5:var l=r.stateNode;r.flags&32&&(Yn(l,""),r.flags&=-33);var o=ka(e);Ci(e,o,l);break;case 3:case 4:var i=r.stateNode.containerInfo,a=ka(e);ki(e,a,i);break;default:throw Error(u(161))}}catch(c){Pe(e,e.return,c)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function lf(e,t,n){M=e,Na(e)}function Na(e,t,n){for(var r=(e.mode&1)!==0;M!==null;){var l=M,o=l.child;if(l.tag===22&&r){var i=l.memoizedState!==null||Nl;if(!i){var a=l.alternate,c=a!==null&&a.memoizedState!==null||Qe;a=Nl;var y=Qe;if(Nl=i,(Qe=c)&&!y)for(M=l;M!==null;)i=M,c=i.child,i.tag===22&&i.memoizedState!==null?ja(l):c!==null?(c.return=i,M=c):ja(l);for(;o!==null;)M=o,Na(o),o=o.sibling;M=l,Nl=a,Qe=y}Pa(e)}else(l.subtreeFlags&8772)!==0&&o!==null?(o.return=l,M=o):Pa(e)}}function Pa(e){for(;M!==null;){var t=M;if((t.flags&8772)!==0){var n=t.alternate;try{if((t.flags&8772)!==0)switch(t.tag){case 0:case 11:case 15:Qe||Pl(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Qe)if(n===null)r.componentDidMount();else{var l=t.elementType===t.type?n.memoizedProps:ht(t.type,n.memoizedProps);r.componentDidUpdate(l,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&zu(t,o,r);break;case 3:var i=t.updateQueue;if(i!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}zu(t,i,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var c=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":c.autoFocus&&n.focus();break;case"img":c.src&&(n.src=c.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var y=t.alternate;if(y!==null){var _=y.memoizedState;if(_!==null){var z=_.dehydrated;z!==null&&or(z)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(u(163))}Qe||t.flags&512&&Si(t)}catch(E){Pe(t,t.return,E)}}if(t===e){M=null;break}if(n=t.sibling,n!==null){n.return=t.return,M=n;break}M=t.return}}function za(e){for(;M!==null;){var t=M;if(t===e){M=null;break}var n=t.sibling;if(n!==null){n.return=t.return,M=n;break}M=t.return}}function ja(e){for(;M!==null;){var t=M;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Pl(4,t)}catch(c){Pe(t,n,c)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var l=t.return;try{r.componentDidMount()}catch(c){Pe(t,l,c)}}var o=t.return;try{Si(t)}catch(c){Pe(t,o,c)}break;case 5:var i=t.return;try{Si(t)}catch(c){Pe(t,i,c)}}}catch(c){Pe(t,t.return,c)}if(t===e){M=null;break}var a=t.sibling;if(a!==null){a.return=t.return,M=a;break}M=t.return}}var of=Math.ceil,zl=ue.ReactCurrentDispatcher,Ei=ue.ReactCurrentOwner,ct=ue.ReactCurrentBatchConfig,se=0,Fe=null,Te=null,$e=0,lt=0,$n=Bt(0),Oe=0,zr=null,pn=0,jl=0,_i=0,jr=null,qe=null,Ni=0,Wn=1/0,Tt=null,Rl=!1,Pi=null,Xt=null,Il=!1,Jt=null,Tl=0,Rr=0,zi=null,Ll=-1,Ml=0;function Ge(){return(se&6)!==0?je():Ll!==-1?Ll:Ll=je()}function Zt(e){return(e.mode&1)===0?1:(se&2)!==0&&$e!==0?$e&-$e:Wd.transition!==null?(Ml===0&&(Ml=ks()),Ml):(e=pe,e!==0||(e=window.event,e=e===void 0?16:Is(e.type)),e)}function yt(e,t,n,r){if(50<Rr)throw Rr=0,zi=null,Error(u(185));er(e,n,r),((se&2)===0||e!==Fe)&&(e===Fe&&((se&2)===0&&(jl|=n),Oe===4&&qt(e,$e)),be(e,r),n===1&&se===0&&(t.mode&1)===0&&(Wn=je()+500,ul&&Qt()))}function be(e,t){var n=e.callbackNode;Wc(e,t);var r=Br(e,e===Fe?$e:0);if(r===0)n!==null&&ws(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&ws(n),t===1)e.tag===0?$d(Ia.bind(null,e)):gu(Ia.bind(null,e)),Dd(function(){(se&6)===0&&Qt()}),n=null;else{switch(Cs(r)){case 1:n=io;break;case 4:n=xs;break;case 16:n=Ur;break;case 536870912:n=Ss;break;default:n=Ur}n=Ua(n,Ra.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Ra(e,t){if(Ll=-1,Ml=0,(se&6)!==0)throw Error(u(327));var n=e.callbackNode;if(Bn()&&e.callbackNode!==n)return null;var r=Br(e,e===Fe?$e:0);if(r===0)return null;if((r&30)!==0||(r&e.expiredLanes)!==0||t)t=Ol(e,r);else{t=r;var l=se;se|=2;var o=La();(Fe!==e||$e!==t)&&(Tt=null,Wn=je()+500,hn(e,t));do try{af();break}catch(a){Ta(e,a)}while(!0);Ko(),zl.current=o,se=l,Te!==null?t=0:(Fe=null,$e=0,t=Oe)}if(t!==0){if(t===2&&(l=so(e),l!==0&&(r=l,t=ji(e,l))),t===1)throw n=zr,hn(e,0),qt(e,r),be(e,je()),n;if(t===6)qt(e,r);else{if(l=e.current.alternate,(r&30)===0&&!sf(l)&&(t=Ol(e,r),t===2&&(o=so(e),o!==0&&(r=o,t=ji(e,o))),t===1))throw n=zr,hn(e,0),qt(e,r),be(e,je()),n;switch(e.finishedWork=l,e.finishedLanes=r,t){case 0:case 1:throw Error(u(345));case 2:gn(e,qe,Tt);break;case 3:if(qt(e,r),(r&130023424)===r&&(t=Ni+500-je(),10<t)){if(Br(e,0)!==0)break;if(l=e.suspendedLanes,(l&r)!==r){Ge(),e.pingedLanes|=e.suspendedLanes&l;break}e.timeoutHandle=Oo(gn.bind(null,e,qe,Tt),t);break}gn(e,qe,Tt);break;case 4:if(qt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,l=-1;0<r;){var i=31-ft(r);o=1<<i,i=t[i],i>l&&(l=i),r&=~o}if(r=l,r=je()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*of(r/1960))-r,10<r){e.timeoutHandle=Oo(gn.bind(null,e,qe,Tt),r);break}gn(e,qe,Tt);break;case 5:gn(e,qe,Tt);break;default:throw Error(u(329))}}}return be(e,je()),e.callbackNode===n?Ra.bind(null,e):null}function ji(e,t){var n=jr;return e.current.memoizedState.isDehydrated&&(hn(e,t).flags|=256),e=Ol(e,t),e!==2&&(t=qe,qe=n,t!==null&&Ri(t)),e}function Ri(e){qe===null?qe=e:qe.push.apply(qe,e)}function sf(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var l=n[r],o=l.getSnapshot;l=l.value;try{if(!pt(o(),l))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function qt(e,t){for(t&=~_i,t&=~jl,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-ft(t),r=1<<n;e[n]=-1,t&=~r}}function Ia(e){if((se&6)!==0)throw Error(u(327));Bn();var t=Br(e,0);if((t&1)===0)return be(e,je()),null;var n=Ol(e,t);if(e.tag!==0&&n===2){var r=so(e);r!==0&&(t=r,n=ji(e,r))}if(n===1)throw n=zr,hn(e,0),qt(e,t),be(e,je()),n;if(n===6)throw Error(u(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,gn(e,qe,Tt),be(e,je()),null}function Ii(e,t){var n=se;se|=1;try{return e(t)}finally{se=n,se===0&&(Wn=je()+500,ul&&Qt())}}function mn(e){Jt!==null&&Jt.tag===0&&(se&6)===0&&Bn();var t=se;se|=1;var n=ct.transition,r=pe;try{if(ct.transition=null,pe=1,e)return e()}finally{pe=r,ct.transition=n,se=t,(se&6)===0&&Qt()}}function Ti(){lt=$n.current,xe($n)}function hn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Ad(n)),Te!==null)for(n=Te.return;n!==null;){var r=n;switch($o(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&il();break;case 3:Fn(),xe(Xe),xe(We),ei();break;case 5:qo(r);break;case 4:Fn();break;case 13:xe(_e);break;case 19:xe(_e);break;case 10:Go(r.type._context);break;case 22:case 23:Ti()}n=n.return}if(Fe=e,Te=e=bt(e.current,null),$e=lt=t,Oe=0,zr=null,_i=jl=pn=0,qe=jr=null,cn!==null){for(t=0;t<cn.length;t++)if(n=cn[t],r=n.interleaved,r!==null){n.interleaved=null;var l=r.next,o=n.pending;if(o!==null){var i=o.next;o.next=l,r.next=i}n.pending=r}cn=null}return e}function Ta(e,t){do{var n=Te;try{if(Ko(),yl.current=kl,wl){for(var r=Ne.memoizedState;r!==null;){var l=r.queue;l!==null&&(l.pending=null),r=r.next}wl=!1}if(fn=0,De=Me=Ne=null,kr=!1,Cr=0,Ei.current=null,n===null||n.return===null){Oe=1,zr=t,Te=null;break}e:{var o=e,i=n.return,a=n,c=t;if(t=$e,a.flags|=32768,c!==null&&typeof c=="object"&&typeof c.then=="function"){var y=c,_=a,z=_.tag;if((_.mode&1)===0&&(z===0||z===11||z===15)){var E=_.alternate;E?(_.updateQueue=E.updateQueue,_.memoizedState=E.memoizedState,_.lanes=E.lanes):(_.updateQueue=null,_.memoizedState=null)}var T=ra(i);if(T!==null){T.flags&=-257,la(T,i,a,o,t),T.mode&1&&na(o,y,t),t=T,c=y;var O=t.updateQueue;if(O===null){var U=new Set;U.add(c),t.updateQueue=U}else O.add(c);break e}else{if((t&1)===0){na(o,y,t),Li();break e}c=Error(u(426))}}else if(ke&&a.mode&1){var Re=ra(i);if(Re!==null){(Re.flags&65536)===0&&(Re.flags|=256),la(Re,i,a,o,t),Ho(Un(c,a));break e}}o=c=Un(c,a),Oe!==4&&(Oe=2),jr===null?jr=[o]:jr.push(o),o=i;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var m=ea(o,c,t);Pu(o,m);break e;case 1:a=c;var f=o.type,h=o.stateNode;if((o.flags&128)===0&&(typeof f.getDerivedStateFromError=="function"||h!==null&&typeof h.componentDidCatch=="function"&&(Xt===null||!Xt.has(h)))){o.flags|=65536,t&=-t,o.lanes|=t;var j=ta(o,a,t);Pu(o,j);break e}}o=o.return}while(o!==null)}Oa(n)}catch(V){t=V,Te===n&&n!==null&&(Te=n=n.return);continue}break}while(!0)}function La(){var e=zl.current;return zl.current=kl,e===null?kl:e}function Li(){(Oe===0||Oe===3||Oe===2)&&(Oe=4),Fe===null||(pn&268435455)===0&&(jl&268435455)===0||qt(Fe,$e)}function Ol(e,t){var n=se;se|=2;var r=La();(Fe!==e||$e!==t)&&(Tt=null,hn(e,t));do try{uf();break}catch(l){Ta(e,l)}while(!0);if(Ko(),se=n,zl.current=r,Te!==null)throw Error(u(261));return Fe=null,$e=0,Oe}function uf(){for(;Te!==null;)Ma(Te)}function af(){for(;Te!==null&&!Lc();)Ma(Te)}function Ma(e){var t=Fa(e.alternate,e,lt);e.memoizedProps=e.pendingProps,t===null?Oa(e):Te=t,Ei.current=null}function Oa(e){var t=e;do{var n=t.alternate;if(e=t.return,(t.flags&32768)===0){if(n=ef(n,t,lt),n!==null){Te=n;return}}else{if(n=tf(n,t),n!==null){n.flags&=32767,Te=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Oe=6,Te=null;return}}if(t=t.sibling,t!==null){Te=t;return}Te=t=e}while(t!==null);Oe===0&&(Oe=5)}function gn(e,t,n){var r=pe,l=ct.transition;try{ct.transition=null,pe=1,cf(e,t,n,r)}finally{ct.transition=l,pe=r}return null}function cf(e,t,n,r){do Bn();while(Jt!==null);if((se&6)!==0)throw Error(u(327));n=e.finishedWork;var l=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(u(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(Bc(e,o),e===Fe&&(Te=Fe=null,$e=0),(n.subtreeFlags&2064)===0&&(n.flags&2064)===0||Il||(Il=!0,Ua(Ur,function(){return Bn(),null})),o=(n.flags&15990)!==0,(n.subtreeFlags&15990)!==0||o){o=ct.transition,ct.transition=null;var i=pe;pe=1;var a=se;se|=4,Ei.current=null,rf(e,n),_a(n,e),jd(Lo),Kr=!!To,Lo=To=null,e.current=n,lf(n),Mc(),se=a,pe=i,ct.transition=o}else e.current=n;if(Il&&(Il=!1,Jt=e,Tl=l),o=e.pendingLanes,o===0&&(Xt=null),Dc(n.stateNode),be(e,je()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)l=t[n],r(l.value,{componentStack:l.stack,digest:l.digest});if(Rl)throw Rl=!1,e=Pi,Pi=null,e;return(Tl&1)!==0&&e.tag!==0&&Bn(),o=e.pendingLanes,(o&1)!==0?e===zi?Rr++:(Rr=0,zi=e):Rr=0,Qt(),null}function Bn(){if(Jt!==null){var e=Cs(Tl),t=ct.transition,n=pe;try{if(ct.transition=null,pe=16>e?16:e,Jt===null)var r=!1;else{if(e=Jt,Jt=null,Tl=0,(se&6)!==0)throw Error(u(331));var l=se;for(se|=4,M=e.current;M!==null;){var o=M,i=o.child;if((M.flags&16)!==0){var a=o.deletions;if(a!==null){for(var c=0;c<a.length;c++){var y=a[c];for(M=y;M!==null;){var _=M;switch(_.tag){case 0:case 11:case 15:Pr(8,_,o)}var z=_.child;if(z!==null)z.return=_,M=z;else for(;M!==null;){_=M;var E=_.sibling,T=_.return;if(xa(_),_===y){M=null;break}if(E!==null){E.return=T,M=E;break}M=T}}}var O=o.alternate;if(O!==null){var U=O.child;if(U!==null){O.child=null;do{var Re=U.sibling;U.sibling=null,U=Re}while(U!==null)}}M=o}}if((o.subtreeFlags&2064)!==0&&i!==null)i.return=o,M=i;else e:for(;M!==null;){if(o=M,(o.flags&2048)!==0)switch(o.tag){case 0:case 11:case 15:Pr(9,o,o.return)}var m=o.sibling;if(m!==null){m.return=o.return,M=m;break e}M=o.return}}var f=e.current;for(M=f;M!==null;){i=M;var h=i.child;if((i.subtreeFlags&2064)!==0&&h!==null)h.return=i,M=h;else e:for(i=f;M!==null;){if(a=M,(a.flags&2048)!==0)try{switch(a.tag){case 0:case 11:case 15:Pl(9,a)}}catch(V){Pe(a,a.return,V)}if(a===i){M=null;break e}var j=a.sibling;if(j!==null){j.return=a.return,M=j;break e}M=a.return}}if(se=l,Qt(),xt&&typeof xt.onPostCommitFiberRoot=="function")try{xt.onPostCommitFiberRoot(Vr,e)}catch{}r=!0}return r}finally{pe=n,ct.transition=t}}return!1}function Aa(e,t,n){t=Un(n,t),t=ea(e,t,1),e=Gt(e,t,1),t=Ge(),e!==null&&(er(e,1,t),be(e,t))}function Pe(e,t,n){if(e.tag===3)Aa(e,e,n);else for(;t!==null;){if(t.tag===3){Aa(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Xt===null||!Xt.has(r))){e=Un(n,e),e=ta(t,e,1),t=Gt(t,e,1),e=Ge(),t!==null&&(er(t,1,e),be(t,e));break}}t=t.return}}function df(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Ge(),e.pingedLanes|=e.suspendedLanes&n,Fe===e&&($e&n)===n&&(Oe===4||Oe===3&&($e&130023424)===$e&&500>je()-Ni?hn(e,0):_i|=n),be(e,t)}function Da(e,t){t===0&&((e.mode&1)===0?t=1:(t=Wr,Wr<<=1,(Wr&130023424)===0&&(Wr=4194304)));var n=Ge();e=jt(e,t),e!==null&&(er(e,t,n),be(e,n))}function ff(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Da(e,n)}function pf(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,l=e.memoizedState;l!==null&&(n=l.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(u(314))}r!==null&&r.delete(t),Da(e,n)}var Fa;Fa=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Xe.current)Ze=!0;else{if((e.lanes&n)===0&&(t.flags&128)===0)return Ze=!1,bd(e,t,n);Ze=(e.flags&131072)!==0}else Ze=!1,ke&&(t.flags&1048576)!==0&&vu(t,cl,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;_l(e,t),e=t.pendingProps;var l=In(t,We.current);Dn(t,n),l=ri(null,t,r,e,l,n);var o=li();return t.flags|=1,typeof l=="object"&&l!==null&&typeof l.render=="function"&&l.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Je(r)?(o=!0,sl(t)):o=!1,t.memoizedState=l.state!==null&&l.state!==void 0?l.state:null,Jo(t),l.updater=Cl,t.stateNode=l,l._reactInternals=t,ci(t,r,e,n),t=mi(null,t,r,!0,o,n)):(t.tag=0,ke&&o&&Vo(t),Ke(null,t,l,n),t=t.child),t;case 16:r=t.elementType;e:{switch(_l(e,t),e=t.pendingProps,l=r._init,r=l(r._payload),t.type=r,l=t.tag=hf(r),e=ht(r,e),l){case 0:t=pi(null,t,r,e,n);break e;case 1:t=ca(null,t,r,e,n);break e;case 11:t=oa(null,t,r,e,n);break e;case 14:t=ia(null,t,r,ht(r.type,e),n);break e}throw Error(u(306,r,""))}return t;case 0:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:ht(r,l),pi(e,t,r,l,n);case 1:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:ht(r,l),ca(e,t,r,l,n);case 3:e:{if(da(t),e===null)throw Error(u(387));r=t.pendingProps,o=t.memoizedState,l=o.element,Nu(e,t),gl(t,r,null,n);var i=t.memoizedState;if(r=i.element,o.isDehydrated)if(o={element:r,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){l=Un(Error(u(423)),t),t=fa(e,t,r,n,l);break e}else if(r!==l){l=Un(Error(u(424)),t),t=fa(e,t,r,n,l);break e}else for(rt=Wt(t.stateNode.containerInfo.firstChild),nt=t,ke=!0,mt=null,n=Eu(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Mn(),r===l){t=It(e,t,n);break e}Ke(e,t,r,n)}t=t.child}return t;case 5:return ju(t),e===null&&Bo(t),r=t.type,l=t.pendingProps,o=e!==null?e.memoizedProps:null,i=l.children,Mo(r,l)?i=null:o!==null&&Mo(r,o)&&(t.flags|=32),aa(e,t),Ke(e,t,i,n),t.child;case 6:return e===null&&Bo(t),null;case 13:return pa(e,t,n);case 4:return Zo(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=On(t,null,r,n):Ke(e,t,r,n),t.child;case 11:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:ht(r,l),oa(e,t,r,l,n);case 7:return Ke(e,t,t.pendingProps,n),t.child;case 8:return Ke(e,t,t.pendingProps.children,n),t.child;case 12:return Ke(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,l=t.pendingProps,o=t.memoizedProps,i=l.value,ve(pl,r._currentValue),r._currentValue=i,o!==null)if(pt(o.value,i)){if(o.children===l.children&&!Xe.current){t=It(e,t,n);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var a=o.dependencies;if(a!==null){i=o.child;for(var c=a.firstContext;c!==null;){if(c.context===r){if(o.tag===1){c=Rt(-1,n&-n),c.tag=2;var y=o.updateQueue;if(y!==null){y=y.shared;var _=y.pending;_===null?c.next=c:(c.next=_.next,_.next=c),y.pending=c}}o.lanes|=n,c=o.alternate,c!==null&&(c.lanes|=n),Yo(o.return,n,t),a.lanes|=n;break}c=c.next}}else if(o.tag===10)i=o.type===t.type?null:o.child;else if(o.tag===18){if(i=o.return,i===null)throw Error(u(341));i.lanes|=n,a=i.alternate,a!==null&&(a.lanes|=n),Yo(i,n,t),i=o.sibling}else i=o.child;if(i!==null)i.return=o;else for(i=o;i!==null;){if(i===t){i=null;break}if(o=i.sibling,o!==null){o.return=i.return,i=o;break}i=i.return}o=i}Ke(e,t,l.children,n),t=t.child}return t;case 9:return l=t.type,r=t.pendingProps.children,Dn(t,n),l=ut(l),r=r(l),t.flags|=1,Ke(e,t,r,n),t.child;case 14:return r=t.type,l=ht(r,t.pendingProps),l=ht(r.type,l),ia(e,t,r,l,n);case 15:return sa(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:ht(r,l),_l(e,t),t.tag=1,Je(r)?(e=!0,sl(t)):e=!1,Dn(t,n),qu(t,r,l),ci(t,r,l,n),mi(null,t,r,!0,e,n);case 19:return ha(e,t,n);case 22:return ua(e,t,n)}throw Error(u(156,t.tag))};function Ua(e,t){return ys(e,t)}function mf(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function dt(e,t,n,r){return new mf(e,t,n,r)}function Mi(e){return e=e.prototype,!(!e||!e.isReactComponent)}function hf(e){if(typeof e=="function")return Mi(e)?1:0;if(e!=null){if(e=e.$$typeof,e===X)return 11;if(e===fe)return 14}return 2}function bt(e,t){var n=e.alternate;return n===null?(n=dt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Al(e,t,n,r,l,o){var i=2;if(r=e,typeof e=="function")Mi(e)&&(i=1);else if(typeof e=="string")i=5;else e:switch(e){case ze:return vn(n.children,l,o,t);case Le:i=8,l|=8;break;case re:return e=dt(12,n,t,l|2),e.elementType=re,e.lanes=o,e;case Ae:return e=dt(13,n,t,l),e.elementType=Ae,e.lanes=o,e;case b:return e=dt(19,n,t,l),e.elementType=b,e.lanes=o,e;case ee:return Dl(n,l,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case ye:i=10;break e;case he:i=9;break e;case X:i=11;break e;case fe:i=14;break e;case ce:i=16,r=null;break e}throw Error(u(130,e==null?e:typeof e,""))}return t=dt(i,n,t,l),t.elementType=e,t.type=r,t.lanes=o,t}function vn(e,t,n,r){return e=dt(7,e,r,t),e.lanes=n,e}function Dl(e,t,n,r){return e=dt(22,e,r,t),e.elementType=ee,e.lanes=n,e.stateNode={isHidden:!1},e}function Oi(e,t,n){return e=dt(6,e,null,t),e.lanes=n,e}function Ai(e,t,n){return t=dt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function gf(e,t,n,r,l){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=uo(0),this.expirationTimes=uo(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=uo(0),this.identifierPrefix=r,this.onRecoverableError=l,this.mutableSourceEagerHydrationData=null}function Di(e,t,n,r,l,o,i,a,c){return e=new gf(e,t,n,a,c),t===1?(t=1,o===!0&&(t|=8)):t=0,o=dt(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Jo(o),e}function vf(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Ee,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Va(e){if(!e)return Ht;e=e._reactInternals;e:{if(ln(e)!==e||e.tag!==1)throw Error(u(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Je(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(u(171))}if(e.tag===1){var n=e.type;if(Je(n))return mu(e,n,t)}return t}function $a(e,t,n,r,l,o,i,a,c){return e=Di(n,r,!0,e,l,o,i,a,c),e.context=Va(null),n=e.current,r=Ge(),l=Zt(n),o=Rt(r,l),o.callback=t??null,Gt(n,o,l),e.current.lanes=l,er(e,l,r),be(e,r),e}function Fl(e,t,n,r){var l=t.current,o=Ge(),i=Zt(l);return n=Va(n),t.context===null?t.context=n:t.pendingContext=n,t=Rt(o,i),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Gt(l,t,i),e!==null&&(yt(e,l,i,o),hl(e,l,i)),i}function Ul(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Wa(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Fi(e,t){Wa(e,t),(e=e.alternate)&&Wa(e,t)}function yf(){return null}var Ba=typeof reportError=="function"?reportError:function(e){console.error(e)};function Ui(e){this._internalRoot=e}Vl.prototype.render=Ui.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(u(409));Fl(e,t,null,null)},Vl.prototype.unmount=Ui.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;mn(function(){Fl(null,e,null,null)}),t[_t]=null}};function Vl(e){this._internalRoot=e}Vl.prototype.unstable_scheduleHydration=function(e){if(e){var t=Ns();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Ut.length&&t!==0&&t<Ut[n].priority;n++);Ut.splice(n,0,e),n===0&&js(e)}};function Vi(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function $l(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Ha(){}function wf(e,t,n,r,l){if(l){if(typeof r=="function"){var o=r;r=function(){var y=Ul(i);o.call(y)}}var i=$a(t,r,e,0,null,!1,!1,"",Ha);return e._reactRootContainer=i,e[_t]=i.current,mr(e.nodeType===8?e.parentNode:e),mn(),i}for(;l=e.lastChild;)e.removeChild(l);if(typeof r=="function"){var a=r;r=function(){var y=Ul(c);a.call(y)}}var c=Di(e,0,!1,null,null,!1,!1,"",Ha);return e._reactRootContainer=c,e[_t]=c.current,mr(e.nodeType===8?e.parentNode:e),mn(function(){Fl(t,c,n,r)}),c}function Wl(e,t,n,r,l){var o=n._reactRootContainer;if(o){var i=o;if(typeof l=="function"){var a=l;l=function(){var c=Ul(i);a.call(c)}}Fl(t,i,e,l)}else i=wf(n,t,e,l,r);return Ul(i)}Es=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=bn(t.pendingLanes);n!==0&&(ao(t,n|1),be(t,je()),(se&6)===0&&(Wn=je()+500,Qt()))}break;case 13:mn(function(){var r=jt(e,1);if(r!==null){var l=Ge();yt(r,e,1,l)}}),Fi(e,1)}},co=function(e){if(e.tag===13){var t=jt(e,134217728);if(t!==null){var n=Ge();yt(t,e,134217728,n)}Fi(e,134217728)}},_s=function(e){if(e.tag===13){var t=Zt(e),n=jt(e,t);if(n!==null){var r=Ge();yt(n,e,t,r)}Fi(e,t)}},Ns=function(){return pe},Ps=function(e,t){var n=pe;try{return pe=e,t()}finally{pe=n}},no=function(e,t,n){switch(t){case"input":if(Yl(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var l=ol(r);if(!l)throw Error(u(90));Ot(r),Yl(r,l)}}}break;case"textarea":ns(e,n);break;case"select":t=n.value,t!=null&&wn(e,!!n.multiple,t,!1)}},ds=Ii,fs=mn;var xf={usingClientEntryPoint:!1,Events:[vr,jn,ol,as,cs,Ii]},Ir={findFiberByHostInstance:on,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Sf={bundleType:Ir.bundleType,version:Ir.version,rendererPackageName:Ir.rendererPackageName,rendererConfig:Ir.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:ue.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=gs(e),e===null?null:e.stateNode},findFiberByHostInstance:Ir.findFiberByHostInstance||yf,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Bl=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Bl.isDisabled&&Bl.supportsFiber)try{Vr=Bl.inject(Sf),xt=Bl}catch{}}return et.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=xf,et.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Vi(t))throw Error(u(200));return vf(e,t,null,n)},et.createRoot=function(e,t){if(!Vi(e))throw Error(u(299));var n=!1,r="",l=Ba;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(l=t.onRecoverableError)),t=Di(e,1,!1,null,null,n,!1,r,l),e[_t]=t.current,mr(e.nodeType===8?e.parentNode:e),new Ui(t)},et.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(u(188)):(e=Object.keys(e).join(","),Error(u(268,e)));return e=gs(t),e=e===null?null:e.stateNode,e},et.flushSync=function(e){return mn(e)},et.hydrate=function(e,t,n){if(!$l(t))throw Error(u(200));return Wl(null,e,t,!0,n)},et.hydrateRoot=function(e,t,n){if(!Vi(e))throw Error(u(405));var r=n!=null&&n.hydratedSources||null,l=!1,o="",i=Ba;if(n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(i=n.onRecoverableError)),t=$a(t,null,e,1,n??null,l,!1,o,i),e[_t]=t.current,mr(e),r)for(e=0;e<r.length;e++)n=r[e],l=n._getVersion,l=l(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,l]:t.mutableSourceEagerHydrationData.push(n,l);return new Vl(t)},et.render=function(e,t,n){if(!$l(t))throw Error(u(200));return Wl(null,e,t,!1,n)},et.unmountComponentAtNode=function(e){if(!$l(e))throw Error(u(40));return e._reactRootContainer?(mn(function(){Wl(null,null,e,!1,function(){e._reactRootContainer=null,e[_t]=null})}),!0):!1},et.unstable_batchedUpdates=Ii,et.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!$l(n))throw Error(u(200));if(e==null||e._reactInternals===void 0)throw Error(u(38));return Wl(e,t,n,!1,r)},et.version="18.3.1-next-f1338f8080-20240426",et}var qa;function ac(){if(qa)return Bi.exports;qa=1;function s(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(s)}catch(d){console.error(d)}}return s(),Bi.exports=zf(),Bi.exports}var ba;function jf(){if(ba)return Hl;ba=1;var s=ac();return Hl.createRoot=s.createRoot,Hl.hydrateRoot=s.hydrateRoot,Hl}var Rf=jf();const wt={title:"小熊波波的友谊冒险",ageGroup:"6-8岁",theme:"友谊",pages:[{id:1,content:"波波是一只住在森林里的小棕熊。他有一双好奇的大眼睛和一颗善良的心。每天早晨，波波都会坐在自己的小木屋前，望着远处的大树和花朵，但他从来不敢走得太远。",isInteractive:!1,imagePath:"/assets/images/page1.png"},{id:2,content:'一天早晨，波波听到一阵欢快的歌声。"那是谁在唱歌呢？"波波好奇地想。他鼓起勇气，第一次离开了自己的小木屋，沿着小路向歌声传来的方向走去。',isInteractive:!1,imagePath:"/assets/images/page2.png"},{id:3,content:"波波来到了一片开阔的草地。他看到一只小兔子正在那里采摘野花。小兔子有着雪白的毛发和粉红的鼻子，正哼着波波从未听过的歌曲。",isInteractive:!1,imagePath:"/assets/images/page3.png"},{id:4,content:"波波想和小兔子交朋友，但他不知道该怎么开始。他站在那里，紧张地搓着自己的小爪子。",isInteractive:!0,interactiveQuestion:"如果你是波波，你会怎么向小兔子打招呼呢？你会说些什么来介绍自己？",guidancePrompt:"让我来帮你思考一下。如果你是波波，你可能会说'你好'，或者'我叫波波'。你也可以问小兔子的名字，或者告诉她你喜欢她的歌声。你想对小兔子说什么呢？",imagePath:"/assets/images/page1.png"},{id:5,content:'小兔子看到了波波，友好地笑了笑。"你好，我叫莉莉！"小兔子说，"我正在采集这些美丽的花朵，准备做一个花环。你想一起来吗？"波波点点头，慢慢地走近了莉莉。',isInteractive:!1,imagePath:"/assets/images/page5.png"},{id:6,content:'波波和莉莉一起采集花朵，他们边采边聊。莉莉告诉波波，森林里还有许多其他的动物朋友。"我们每周五都会在大橡树下举行野餐会，"莉莉说，"你愿意来参加吗？"',isInteractive:!1,imagePath:"/assets/images/page6.png"},{id:7,content:"波波既兴奋又紧张。他从来没有参加过野餐会，也没有见过那么多的动物朋友。但莉莉的笑容让他感到安心，于是他答应了。",isInteractive:!1,imagePath:"/assets/images/page7.png"},{id:8,content:"周五到了，波波来到了大橡树下。那里已经聚集了许多动物：一只聪明的猫头鹰、一对活泼的松鼠兄弟、一只慢吞吞的乌龟，还有莉莉。波波站在一旁，不知道该做什么。",isInteractive:!0,interactiveQuestion:"波波看到这么多新朋友有点害怕。如果你是波波，你会怎么融入这个新的朋友圈子？你会先和谁说话，为什么？",guidancePrompt:"让我来帮你思考一下。波波可以先和他认识的莉莉打招呼，因为她已经是他的朋友了。或者，他可以去和看起来友善的乌龟说话，因为乌龟说话慢，可能不会让波波感到压力。你觉得波波应该怎么做呢？",imagePath:"/assets/images/page2.png"},{id:9,content:"野餐会上，大家分享了各自带来的美食。猫头鹰带来了蜂蜜饼干，松鼠兄弟带来了坚果沙拉，乌龟带来了新鲜的浆果。波波没有带任何东西，他感到有些难过。",isInteractive:!1,imagePath:"/assets/images/page9.png"},{id:10,content:'"别担心，波波，"莉莉轻声说，"重要的不是你带了什么，而是你来了。友谊不是用礼物来衡量的，而是用心来感受的。"波波听了，心里暖暖的。',isInteractive:!1,imagePath:"/assets/images/page10.png"},{id:11,content:"随着时间的推移，波波和森林里的动物们成为了好朋友。他们一起玩耍，一起解决问题，一起度过了许多快乐的时光。波波不再是那个害羞的小熊了，他学会了分享、倾听和关心他人。",isInteractive:!0,interactiveQuestion:"友谊给波波带来了哪些变化？你能想到一个你和朋友一起解决问题的经历吗？请分享这个故事。",guidancePrompt:"让我来帮你思考一下。友谊让波波变得更勇敢、更快乐了。你有没有和朋友一起解决过问题？比如一起完成作业，一起整理玩具，或者一起想办法帮助别人？请告诉我你的经历。",imagePath:"/assets/images/page3.png"},{id:12,content:"现在，每天早晨，波波都会兴高采烈地离开自己的小木屋，去拜访他的朋友们。他知道，真正的友谊需要勇气去开始，需要时间去培养，更需要真心去维护。在森林里，波波找到了属于自己的幸福。",isInteractive:!1,imagePath:"/assets/images/page12.png"}]};function ec(s,d){if(typeof s=="function")return s(d);s!=null&&(s.current=d)}function If(...s){return d=>{let u=!1;const v=s.map(g=>{const x=ec(g,d);return!u&&typeof x=="function"&&(u=!0),x});if(u)return()=>{for(let g=0;g<v.length;g++){const x=v[g];typeof x=="function"?x():ec(s[g],null)}}}}function Tf(...s){return $.useCallback(If(...s),s)}function cc(s){const d=Mf(s),u=$.forwardRef((v,g)=>{const{children:x,...N}=v,S=$.Children.toArray(x),k=S.find(Af);if(k){const L=k.props.children,W=S.map(A=>A===k?$.Children.count(L)>1?$.Children.only(null):$.isValidElement(L)?L.props.children:null:A);return w.jsx(d,{...N,ref:g,children:$.isValidElement(L)?$.cloneElement(L,void 0,W):null})}return w.jsx(d,{...N,ref:g,children:x})});return u.displayName=`${s}.Slot`,u}var Lf=cc("Slot");function Mf(s){const d=$.forwardRef((u,v)=>{const{children:g,...x}=u,N=$.isValidElement(g)?Ff(g):void 0,S=Tf(N,v);if($.isValidElement(g)){const k=Df(x,g.props);return g.type!==$.Fragment&&(k.ref=S),$.cloneElement(g,k)}return $.Children.count(g)>1?$.Children.only(null):null});return d.displayName=`${s}.SlotClone`,d}var Of=Symbol("radix.slottable");function Af(s){return $.isValidElement(s)&&typeof s.type=="function"&&"__radixId"in s.type&&s.type.__radixId===Of}function Df(s,d){const u={...d};for(const v in d){const g=s[v],x=d[v];/^on[A-Z]/.test(v)?g&&x?u[v]=(...S)=>{const k=x(...S);return g(...S),k}:g&&(u[v]=g):v==="style"?u[v]={...g,...x}:v==="className"&&(u[v]=[g,x].filter(Boolean).join(" "))}return{...s,...u}}function Ff(s){var v,g;let d=(v=Object.getOwnPropertyDescriptor(s.props,"ref"))==null?void 0:v.get,u=d&&"isReactWarning"in d&&d.isReactWarning;return u?s.ref:(d=(g=Object.getOwnPropertyDescriptor(s,"ref"))==null?void 0:g.get,u=d&&"isReactWarning"in d&&d.isReactWarning,u?s.props.ref:s.props.ref||s.ref)}function dc(s){var d,u,v="";if(typeof s=="string"||typeof s=="number")v+=s;else if(typeof s=="object")if(Array.isArray(s)){var g=s.length;for(d=0;d<g;d++)s[d]&&(u=dc(s[d]))&&(v&&(v+=" "),v+=u)}else for(u in s)s[u]&&(v&&(v+=" "),v+=u);return v}function fc(){for(var s,d,u=0,v="",g=arguments.length;u<g;u++)(s=arguments[u])&&(d=dc(s))&&(v&&(v+=" "),v+=d);return v}const tc=s=>typeof s=="boolean"?`${s}`:s===0?"0":s,nc=fc,Uf=(s,d)=>u=>{var v;if((d==null?void 0:d.variants)==null)return nc(s,u==null?void 0:u.class,u==null?void 0:u.className);const{variants:g,defaultVariants:x}=d,N=Object.keys(g).map(L=>{const W=u==null?void 0:u[L],A=x==null?void 0:x[L];if(W===null)return null;const D=tc(W)||tc(A);return g[L][D]}),S=u&&Object.entries(u).reduce((L,W)=>{let[A,D]=W;return D===void 0||(L[A]=D),L},{}),k=d==null||(v=d.compoundVariants)===null||v===void 0?void 0:v.reduce((L,W)=>{let{class:A,className:D,...ne}=W;return Object.entries(ne).every(q=>{let[B,I]=q;return Array.isArray(I)?I.includes({...x,...S}[B]):{...x,...S}[B]===I})?[...L,A,D]:L},[]);return nc(s,N,k,u==null?void 0:u.class,u==null?void 0:u.className)},Xi="-",Vf=s=>{const d=Wf(s),{conflictingClassGroups:u,conflictingClassGroupModifiers:v}=s;return{getClassGroupId:N=>{const S=N.split(Xi);return S[0]===""&&S.length!==1&&S.shift(),pc(S,d)||$f(N)},getConflictingClassGroupIds:(N,S)=>{const k=u[N]||[];return S&&v[N]?[...k,...v[N]]:k}}},pc=(s,d)=>{var N;if(s.length===0)return d.classGroupId;const u=s[0],v=d.nextPart.get(u),g=v?pc(s.slice(1),v):void 0;if(g)return g;if(d.validators.length===0)return;const x=s.join(Xi);return(N=d.validators.find(({validator:S})=>S(x)))==null?void 0:N.classGroupId},rc=/^\[(.+)\]$/,$f=s=>{if(rc.test(s)){const d=rc.exec(s)[1],u=d==null?void 0:d.substring(0,d.indexOf(":"));if(u)return"arbitrary.."+u}},Wf=s=>{const{theme:d,prefix:u}=s,v={nextPart:new Map,validators:[]};return Hf(Object.entries(s.classGroups),u).forEach(([x,N])=>{Gi(N,v,x,d)}),v},Gi=(s,d,u,v)=>{s.forEach(g=>{if(typeof g=="string"){const x=g===""?d:lc(d,g);x.classGroupId=u;return}if(typeof g=="function"){if(Bf(g)){Gi(g(v),d,u,v);return}d.validators.push({validator:g,classGroupId:u});return}Object.entries(g).forEach(([x,N])=>{Gi(N,lc(d,x),u,v)})})},lc=(s,d)=>{let u=s;return d.split(Xi).forEach(v=>{u.nextPart.has(v)||u.nextPart.set(v,{nextPart:new Map,validators:[]}),u=u.nextPart.get(v)}),u},Bf=s=>s.isThemeGetter,Hf=(s,d)=>d?s.map(([u,v])=>{const g=v.map(x=>typeof x=="string"?d+x:typeof x=="object"?Object.fromEntries(Object.entries(x).map(([N,S])=>[d+N,S])):x);return[u,g]}):s,Qf=s=>{if(s<1)return{get:()=>{},set:()=>{}};let d=0,u=new Map,v=new Map;const g=(x,N)=>{u.set(x,N),d++,d>s&&(d=0,v=u,u=new Map)};return{get(x){let N=u.get(x);if(N!==void 0)return N;if((N=v.get(x))!==void 0)return g(x,N),N},set(x,N){u.has(x)?u.set(x,N):g(x,N)}}},mc="!",Kf=s=>{const{separator:d,experimentalParseClassName:u}=s,v=d.length===1,g=d[0],x=d.length,N=S=>{const k=[];let L=0,W=0,A;for(let I=0;I<S.length;I++){let Y=S[I];if(L===0){if(Y===g&&(v||S.slice(I,I+x)===d)){k.push(S.slice(W,I)),W=I+x;continue}if(Y==="/"){A=I;continue}}Y==="["?L++:Y==="]"&&L--}const D=k.length===0?S:S.substring(W),ne=D.startsWith(mc),q=ne?D.substring(1):D,B=A&&A>W?A-W:void 0;return{modifiers:k,hasImportantModifier:ne,baseClassName:q,maybePostfixModifierPosition:B}};return u?S=>u({className:S,parseClassName:N}):N},Gf=s=>{if(s.length<=1)return s;const d=[];let u=[];return s.forEach(v=>{v[0]==="["?(d.push(...u.sort(),v),u=[]):u.push(v)}),d.push(...u.sort()),d},Yf=s=>({cache:Qf(s.cacheSize),parseClassName:Kf(s),...Vf(s)}),Xf=/\s+/,Jf=(s,d)=>{const{parseClassName:u,getClassGroupId:v,getConflictingClassGroupIds:g}=d,x=[],N=s.trim().split(Xf);let S="";for(let k=N.length-1;k>=0;k-=1){const L=N[k],{modifiers:W,hasImportantModifier:A,baseClassName:D,maybePostfixModifierPosition:ne}=u(L);let q=!!ne,B=v(q?D.substring(0,ne):D);if(!B){if(!q){S=L+(S.length>0?" "+S:S);continue}if(B=v(D),!B){S=L+(S.length>0?" "+S:S);continue}q=!1}const I=Gf(W).join(":"),Y=A?I+mc:I,oe=Y+B;if(x.includes(oe))continue;x.push(oe);const Ce=g(B,q);for(let ue=0;ue<Ce.length;++ue){const Ie=Ce[ue];x.push(Y+Ie)}S=L+(S.length>0?" "+S:S)}return S};function Zf(){let s=0,d,u,v="";for(;s<arguments.length;)(d=arguments[s++])&&(u=hc(d))&&(v&&(v+=" "),v+=u);return v}const hc=s=>{if(typeof s=="string")return s;let d,u="";for(let v=0;v<s.length;v++)s[v]&&(d=hc(s[v]))&&(u&&(u+=" "),u+=d);return u};function qf(s,...d){let u,v,g,x=N;function N(k){const L=d.reduce((W,A)=>A(W),s());return u=Yf(L),v=u.cache.get,g=u.cache.set,x=S,S(k)}function S(k){const L=v(k);if(L)return L;const W=Jf(k,u);return g(k,W),W}return function(){return x(Zf.apply(null,arguments))}}const Se=s=>{const d=u=>u[s]||[];return d.isThemeGetter=!0,d},gc=/^\[(?:([a-z-]+):)?(.+)\]$/i,bf=/^\d+\/\d+$/,ep=new Set(["px","full","screen"]),tp=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,np=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,rp=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,lp=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,op=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Lt=s=>Qn(s)||ep.has(s)||bf.test(s),tn=s=>Kn(s,"length",pp),Qn=s=>!!s&&!Number.isNaN(Number(s)),Ki=s=>Kn(s,"number",Qn),Lr=s=>!!s&&Number.isInteger(Number(s)),ip=s=>s.endsWith("%")&&Qn(s.slice(0,-1)),J=s=>gc.test(s),nn=s=>tp.test(s),sp=new Set(["length","size","percentage"]),up=s=>Kn(s,sp,vc),ap=s=>Kn(s,"position",vc),cp=new Set(["image","url"]),dp=s=>Kn(s,cp,hp),fp=s=>Kn(s,"",mp),Mr=()=>!0,Kn=(s,d,u)=>{const v=gc.exec(s);return v?v[1]?typeof d=="string"?v[1]===d:d.has(v[1]):u(v[2]):!1},pp=s=>np.test(s)&&!rp.test(s),vc=()=>!1,mp=s=>lp.test(s),hp=s=>op.test(s),gp=()=>{const s=Se("colors"),d=Se("spacing"),u=Se("blur"),v=Se("brightness"),g=Se("borderColor"),x=Se("borderRadius"),N=Se("borderSpacing"),S=Se("borderWidth"),k=Se("contrast"),L=Se("grayscale"),W=Se("hueRotate"),A=Se("invert"),D=Se("gap"),ne=Se("gradientColorStops"),q=Se("gradientColorStopPositions"),B=Se("inset"),I=Se("margin"),Y=Se("opacity"),oe=Se("padding"),Ce=Se("saturate"),ue=Se("scale"),Ie=Se("sepia"),Ee=Se("skew"),ze=Se("space"),Le=Se("translate"),re=()=>["auto","contain","none"],ye=()=>["auto","hidden","clip","visible","scroll"],he=()=>["auto",J,d],X=()=>[J,d],Ae=()=>["",Lt,tn],b=()=>["auto",Qn,J],fe=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],ce=()=>["solid","dashed","dotted","double","none"],ee=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],P=()=>["start","end","center","between","around","evenly","stretch"],F=()=>["","0",J],R=()=>["auto","avoid","all","avoid-page","page","left","right","column"],p=()=>[Qn,J];return{cacheSize:500,separator:":",theme:{colors:[Mr],spacing:[Lt,tn],blur:["none","",nn,J],brightness:p(),borderColor:[s],borderRadius:["none","","full",nn,J],borderSpacing:X(),borderWidth:Ae(),contrast:p(),grayscale:F(),hueRotate:p(),invert:F(),gap:X(),gradientColorStops:[s],gradientColorStopPositions:[ip,tn],inset:he(),margin:he(),opacity:p(),padding:X(),saturate:p(),scale:p(),sepia:F(),skew:p(),space:X(),translate:X()},classGroups:{aspect:[{aspect:["auto","square","video",J]}],container:["container"],columns:[{columns:[nn]}],"break-after":[{"break-after":R()}],"break-before":[{"break-before":R()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...fe(),J]}],overflow:[{overflow:ye()}],"overflow-x":[{"overflow-x":ye()}],"overflow-y":[{"overflow-y":ye()}],overscroll:[{overscroll:re()}],"overscroll-x":[{"overscroll-x":re()}],"overscroll-y":[{"overscroll-y":re()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[B]}],"inset-x":[{"inset-x":[B]}],"inset-y":[{"inset-y":[B]}],start:[{start:[B]}],end:[{end:[B]}],top:[{top:[B]}],right:[{right:[B]}],bottom:[{bottom:[B]}],left:[{left:[B]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Lr,J]}],basis:[{basis:he()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",J]}],grow:[{grow:F()}],shrink:[{shrink:F()}],order:[{order:["first","last","none",Lr,J]}],"grid-cols":[{"grid-cols":[Mr]}],"col-start-end":[{col:["auto",{span:["full",Lr,J]},J]}],"col-start":[{"col-start":b()}],"col-end":[{"col-end":b()}],"grid-rows":[{"grid-rows":[Mr]}],"row-start-end":[{row:["auto",{span:[Lr,J]},J]}],"row-start":[{"row-start":b()}],"row-end":[{"row-end":b()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",J]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",J]}],gap:[{gap:[D]}],"gap-x":[{"gap-x":[D]}],"gap-y":[{"gap-y":[D]}],"justify-content":[{justify:["normal",...P()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...P(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...P(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[oe]}],px:[{px:[oe]}],py:[{py:[oe]}],ps:[{ps:[oe]}],pe:[{pe:[oe]}],pt:[{pt:[oe]}],pr:[{pr:[oe]}],pb:[{pb:[oe]}],pl:[{pl:[oe]}],m:[{m:[I]}],mx:[{mx:[I]}],my:[{my:[I]}],ms:[{ms:[I]}],me:[{me:[I]}],mt:[{mt:[I]}],mr:[{mr:[I]}],mb:[{mb:[I]}],ml:[{ml:[I]}],"space-x":[{"space-x":[ze]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[ze]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",J,d]}],"min-w":[{"min-w":[J,d,"min","max","fit"]}],"max-w":[{"max-w":[J,d,"none","full","min","max","fit","prose",{screen:[nn]},nn]}],h:[{h:[J,d,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[J,d,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[J,d,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[J,d,"auto","min","max","fit"]}],"font-size":[{text:["base",nn,tn]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Ki]}],"font-family":[{font:[Mr]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",J]}],"line-clamp":[{"line-clamp":["none",Qn,Ki]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Lt,J]}],"list-image":[{"list-image":["none",J]}],"list-style-type":[{list:["none","disc","decimal",J]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[s]}],"placeholder-opacity":[{"placeholder-opacity":[Y]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[s]}],"text-opacity":[{"text-opacity":[Y]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ce(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Lt,tn]}],"underline-offset":[{"underline-offset":["auto",Lt,J]}],"text-decoration-color":[{decoration:[s]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:X()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",J]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",J]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[Y]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...fe(),ap]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",up]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},dp]}],"bg-color":[{bg:[s]}],"gradient-from-pos":[{from:[q]}],"gradient-via-pos":[{via:[q]}],"gradient-to-pos":[{to:[q]}],"gradient-from":[{from:[ne]}],"gradient-via":[{via:[ne]}],"gradient-to":[{to:[ne]}],rounded:[{rounded:[x]}],"rounded-s":[{"rounded-s":[x]}],"rounded-e":[{"rounded-e":[x]}],"rounded-t":[{"rounded-t":[x]}],"rounded-r":[{"rounded-r":[x]}],"rounded-b":[{"rounded-b":[x]}],"rounded-l":[{"rounded-l":[x]}],"rounded-ss":[{"rounded-ss":[x]}],"rounded-se":[{"rounded-se":[x]}],"rounded-ee":[{"rounded-ee":[x]}],"rounded-es":[{"rounded-es":[x]}],"rounded-tl":[{"rounded-tl":[x]}],"rounded-tr":[{"rounded-tr":[x]}],"rounded-br":[{"rounded-br":[x]}],"rounded-bl":[{"rounded-bl":[x]}],"border-w":[{border:[S]}],"border-w-x":[{"border-x":[S]}],"border-w-y":[{"border-y":[S]}],"border-w-s":[{"border-s":[S]}],"border-w-e":[{"border-e":[S]}],"border-w-t":[{"border-t":[S]}],"border-w-r":[{"border-r":[S]}],"border-w-b":[{"border-b":[S]}],"border-w-l":[{"border-l":[S]}],"border-opacity":[{"border-opacity":[Y]}],"border-style":[{border:[...ce(),"hidden"]}],"divide-x":[{"divide-x":[S]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[S]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[Y]}],"divide-style":[{divide:ce()}],"border-color":[{border:[g]}],"border-color-x":[{"border-x":[g]}],"border-color-y":[{"border-y":[g]}],"border-color-s":[{"border-s":[g]}],"border-color-e":[{"border-e":[g]}],"border-color-t":[{"border-t":[g]}],"border-color-r":[{"border-r":[g]}],"border-color-b":[{"border-b":[g]}],"border-color-l":[{"border-l":[g]}],"divide-color":[{divide:[g]}],"outline-style":[{outline:["",...ce()]}],"outline-offset":[{"outline-offset":[Lt,J]}],"outline-w":[{outline:[Lt,tn]}],"outline-color":[{outline:[s]}],"ring-w":[{ring:Ae()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[s]}],"ring-opacity":[{"ring-opacity":[Y]}],"ring-offset-w":[{"ring-offset":[Lt,tn]}],"ring-offset-color":[{"ring-offset":[s]}],shadow:[{shadow:["","inner","none",nn,fp]}],"shadow-color":[{shadow:[Mr]}],opacity:[{opacity:[Y]}],"mix-blend":[{"mix-blend":[...ee(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":ee()}],filter:[{filter:["","none"]}],blur:[{blur:[u]}],brightness:[{brightness:[v]}],contrast:[{contrast:[k]}],"drop-shadow":[{"drop-shadow":["","none",nn,J]}],grayscale:[{grayscale:[L]}],"hue-rotate":[{"hue-rotate":[W]}],invert:[{invert:[A]}],saturate:[{saturate:[Ce]}],sepia:[{sepia:[Ie]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[u]}],"backdrop-brightness":[{"backdrop-brightness":[v]}],"backdrop-contrast":[{"backdrop-contrast":[k]}],"backdrop-grayscale":[{"backdrop-grayscale":[L]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[W]}],"backdrop-invert":[{"backdrop-invert":[A]}],"backdrop-opacity":[{"backdrop-opacity":[Y]}],"backdrop-saturate":[{"backdrop-saturate":[Ce]}],"backdrop-sepia":[{"backdrop-sepia":[Ie]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[N]}],"border-spacing-x":[{"border-spacing-x":[N]}],"border-spacing-y":[{"border-spacing-y":[N]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",J]}],duration:[{duration:p()}],ease:[{ease:["linear","in","out","in-out",J]}],delay:[{delay:p()}],animate:[{animate:["none","spin","ping","pulse","bounce",J]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[ue]}],"scale-x":[{"scale-x":[ue]}],"scale-y":[{"scale-y":[ue]}],rotate:[{rotate:[Lr,J]}],"translate-x":[{"translate-x":[Le]}],"translate-y":[{"translate-y":[Le]}],"skew-x":[{"skew-x":[Ee]}],"skew-y":[{"skew-y":[Ee]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",J]}],accent:[{accent:["auto",s]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",J]}],"caret-color":[{caret:[s]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":X()}],"scroll-mx":[{"scroll-mx":X()}],"scroll-my":[{"scroll-my":X()}],"scroll-ms":[{"scroll-ms":X()}],"scroll-me":[{"scroll-me":X()}],"scroll-mt":[{"scroll-mt":X()}],"scroll-mr":[{"scroll-mr":X()}],"scroll-mb":[{"scroll-mb":X()}],"scroll-ml":[{"scroll-ml":X()}],"scroll-p":[{"scroll-p":X()}],"scroll-px":[{"scroll-px":X()}],"scroll-py":[{"scroll-py":X()}],"scroll-ps":[{"scroll-ps":X()}],"scroll-pe":[{"scroll-pe":X()}],"scroll-pt":[{"scroll-pt":X()}],"scroll-pr":[{"scroll-pr":X()}],"scroll-pb":[{"scroll-pb":X()}],"scroll-pl":[{"scroll-pl":X()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",J]}],fill:[{fill:[s,"none"]}],"stroke-w":[{stroke:[Lt,tn,Ki]}],stroke:[{stroke:[s,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},vp=qf(gp);function Mt(...s){return vp(fc(s))}const yp=Uf("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-zinc-950 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 dark:focus-visible:ring-zinc-300",{variants:{variant:{default:"bg-zinc-900 text-zinc-50 shadow hover:bg-zinc-900/90 dark:bg-zinc-50 dark:text-zinc-900 dark:hover:bg-zinc-50/90",destructive:"bg-red-500 text-zinc-50 shadow-sm hover:bg-red-500/90 dark:bg-red-900 dark:text-zinc-50 dark:hover:bg-red-900/90",outline:"border border-zinc-200 bg-white shadow-sm hover:bg-zinc-100 hover:text-zinc-900 dark:border-zinc-800 dark:bg-zinc-950 dark:hover:bg-zinc-800 dark:hover:text-zinc-50",secondary:"bg-zinc-100 text-zinc-900 shadow-sm hover:bg-zinc-100/80 dark:bg-zinc-800 dark:text-zinc-50 dark:hover:bg-zinc-800/80",ghost:"hover:bg-zinc-100 hover:text-zinc-900 dark:hover:bg-zinc-800 dark:hover:text-zinc-50",link:"text-zinc-900 underline-offset-4 hover:underline dark:text-zinc-50"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),ot=$.forwardRef(({className:s,variant:d,size:u,asChild:v=!1,...g},x)=>{const N=v?Lf:"button";return w.jsx(N,{className:Mt(yp({variant:d,size:u,className:s})),ref:x,...g})});ot.displayName="Button";const yc=$.forwardRef(({className:s,...d},u)=>w.jsx("textarea",{className:Mt("flex min-h-[60px] w-full rounded-md border border-zinc-200 bg-transparent px-3 py-2 text-base shadow-sm placeholder:text-zinc-500 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-zinc-950 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm dark:border-zinc-800 dark:placeholder:text-zinc-400 dark:focus-visible:ring-zinc-300",s),ref:u,...d}));yc.displayName="Textarea";const wc=$.forwardRef(({className:s,...d},u)=>w.jsx("div",{ref:u,className:Mt("rounded-xl border border-zinc-200 bg-white text-zinc-950 shadow dark:border-zinc-800 dark:bg-zinc-950 dark:text-zinc-50",s),...d}));wc.displayName="Card";const wp=$.forwardRef(({className:s,...d},u)=>w.jsx("div",{ref:u,className:Mt("flex flex-col space-y-1.5 p-6",s),...d}));wp.displayName="CardHeader";const xp=$.forwardRef(({className:s,...d},u)=>w.jsx("div",{ref:u,className:Mt("font-semibold leading-none tracking-tight",s),...d}));xp.displayName="CardTitle";const Sp=$.forwardRef(({className:s,...d},u)=>w.jsx("div",{ref:u,className:Mt("text-sm text-zinc-500 dark:text-zinc-400",s),...d}));Sp.displayName="CardDescription";const kp=$.forwardRef(({className:s,...d},u)=>w.jsx("div",{ref:u,className:Mt("p-6 pt-0",s),...d}));kp.displayName="CardContent";const Cp=$.forwardRef(({className:s,...d},u)=>w.jsx("div",{ref:u,className:Mt("flex items-center p-6 pt-0",s),...d}));Cp.displayName="CardFooter";function Ep({page:s,onNext:d,onResponseSubmit:u,userResponses:v}){const[g,x]=$.useState(""),[N,S]=$.useState(!1),[k,L]=$.useState(30),[W,A]=$.useState(!1),[D,ne]=$.useState(!1),[q,B]=$.useState(!1),I=$.useRef(null),Y=$.useRef(null),oe=$.useRef(!1);$.useEffect(()=>{const re=v.find(he=>he.pageId===s.id);re?(x(re.response),A(!0)):(x(""),A(!1)),L(30),S(!1),oe.current=!1,"speechSynthesis"in window&&window.speechSynthesis.cancel();const ye=s.isInteractive?`${s.content} ${s.interactiveQuestion}`:s.content;return setTimeout(()=>{ue(ye)},300),()=>{if(Y.current&&clearInterval(Y.current),I.current)try{I.current.abort()}catch(he){console.error("停止语音识别失败",he)}}},[s.id,v]);const Ce=()=>{if("SpeechRecognition"in window||"webkitSpeechRecognition"in window){const re=window.SpeechRecognition||window.webkitSpeechRecognition;I.current=new re,I.current.continuous=!0,I.current.interimResults=!0,I.current.lang="zh-CN",I.current.onresult=ye=>{const he=Array.from(ye.results).map(X=>X[0]).map(X=>X.transcript).join("");x(he)},I.current.onerror=ye=>{console.error("Speech recognition error",ye.error),ne(!1)},I.current.onend=()=>{ne(!1)}}},ue=re=>{if(!("speechSynthesis"in window)){console.error("浏览器不支持语音合成");return}const ye=window.speechSynthesis;ye.speaking&&ye.cancel(),B(!0);const he=new SpeechSynthesisUtterance(re);he.lang="zh-CN",he.rate=.9,he.pitch=1,he.onstart=()=>{B(!0)},he.onend=()=>{B(!1),s.isInteractive&&!W&&!oe.current&&Ie()},ye.speak(he)},Ie=()=>{Y.current&&clearInterval(Y.current),oe.current=!0,Y.current=window.setInterval(()=>{L(re=>re<=1?(Y.current&&clearInterval(Y.current),S(!0),s.guidancePrompt&&ue(s.guidancePrompt),0):re-1)},1e3)};$.useEffect(()=>{Ce()},[]);const Ee=()=>{if(I.current||Ce(),D){if(I.current)try{I.current.stop()}catch(re){console.error("停止语音识别失败",re)}ne(!1)}else if(I.current)try{I.current.start(),ne(!0)}catch(re){console.error("启动语音识别失败",re),alert("启动语音识别失败，请确保您的浏览器支持此功能并已授予麦克风权限。")}else alert("您的浏览器不支持语音识别功能。")},ze=()=>{if(g.trim()){if(D&&I.current)try{I.current.stop()}catch(re){console.error("停止语音识别失败",re)}u(s.id,g),A(!0),ue("谢谢你的回答！"),setTimeout(d,2500)}},Le=()=>{const re=s.isInteractive?`${s.content} ${s.interactiveQuestion}`:s.content;ue(re)};return w.jsxs("div",{className:"flex flex-col items-center max-w-4xl mx-auto p-4",children:[w.jsxs("h2",{className:"text-2xl font-bold mb-4",children:["第",s.id,"页",s.isInteractive?" - 交互环节":""]}),w.jsx("div",{className:"w-full mb-6",children:w.jsx("img",{src:s.imagePath,alt:`故事第${s.id}页插图`,className:"w-full max-h-[400px] object-contain rounded-lg shadow-md"})}),w.jsxs(wc,{className:"w-full p-4 mb-6",children:[w.jsx("div",{className:"flex justify-end mb-2",children:w.jsx(ot,{variant:"outline",size:"sm",onClick:Le,disabled:q,children:q?"正在朗读...":"朗读内容"})}),w.jsx("p",{className:"text-lg mb-4",children:s.content}),s.isInteractive&&w.jsxs("div",{className:"mt-4 border-t pt-4",children:[w.jsx("h3",{className:"text-xl font-semibold mb-2",children:"交互问题："}),w.jsx("p",{className:"mb-4",children:s.interactiveQuestion}),N&&!W&&w.jsx("div",{className:"bg-amber-50 border border-amber-200 p-3 rounded-md mb-4",children:w.jsx("p",{className:"text-amber-800",children:s.guidancePrompt})}),W?w.jsx("div",{className:"bg-green-50 border border-green-200 p-3 rounded-md",children:w.jsx("p",{className:"text-green-800",children:"谢谢你的回答！"})}):w.jsxs(w.Fragment,{children:[w.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[w.jsx(ot,{variant:D?"destructive":"default",onClick:Ee,children:D?"停止录音":"开始语音输入"}),w.jsx("span",{className:"text-sm text-gray-500",children:D&&"正在聆听...请说话"})]}),w.jsx(yc,{value:g,onChange:re=>x(re.target.value),placeholder:"请在这里输入你的回答，或使用语音输入...",className:"min-h-[120px] mb-2"}),w.jsxs("div",{className:"flex justify-between items-center",children:[w.jsxs("span",{className:"text-sm text-gray-500",children:[k>0&&`剩余时间: ${k}秒`,k===0&&"时间到"]}),w.jsx(ot,{onClick:ze,disabled:!g.trim(),children:"提交回答"})]})]})]}),!s.isInteractive&&w.jsx("div",{className:"flex justify-end mt-4",children:w.jsx(ot,{onClick:d,children:"继续阅读"})})]})]})}function _p(s,d=[]){let u=[];function v(x,N){const S=$.createContext(N),k=u.length;u=[...u,N];const L=A=>{var Y;const{scope:D,children:ne,...q}=A,B=((Y=D==null?void 0:D[s])==null?void 0:Y[k])||S,I=$.useMemo(()=>q,Object.values(q));return w.jsx(B.Provider,{value:I,children:ne})};L.displayName=x+"Provider";function W(A,D){var B;const ne=((B=D==null?void 0:D[s])==null?void 0:B[k])||S,q=$.useContext(ne);if(q)return q;if(N!==void 0)return N;throw new Error(`\`${A}\` must be used within \`${x}\``)}return[L,W]}const g=()=>{const x=u.map(N=>$.createContext(N));return function(S){const k=(S==null?void 0:S[s])||x;return $.useMemo(()=>({[`__scope${s}`]:{...S,[s]:k}}),[S,k])}};return g.scopeName=s,[v,Np(g,...d)]}function Np(...s){const d=s[0];if(s.length===1)return d;const u=()=>{const v=s.map(g=>({useScope:g(),scopeName:g.scopeName}));return function(x){const N=v.reduce((S,{useScope:k,scopeName:L})=>{const A=k(x)[`__scope${L}`];return{...S,...A}},{});return $.useMemo(()=>({[`__scope${d.scopeName}`]:N}),[N])}};return u.scopeName=d.scopeName,u}ac();var Pp=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],xc=Pp.reduce((s,d)=>{const u=cc(`Primitive.${d}`),v=$.forwardRef((g,x)=>{const{asChild:N,...S}=g,k=N?u:d;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),w.jsx(k,{...S,ref:x})});return v.displayName=`Primitive.${d}`,{...s,[d]:v}},{}),Ji="Progress",Zi=100,[zp,Qp]=_p(Ji),[jp,Rp]=zp(Ji),Sc=$.forwardRef((s,d)=>{const{__scopeProgress:u,value:v=null,max:g,getValueLabel:x=Ip,...N}=s;(g||g===0)&&!oc(g)&&console.error(Tp(`${g}`,"Progress"));const S=oc(g)?g:Zi;v!==null&&!ic(v,S)&&console.error(Lp(`${v}`,"Progress"));const k=ic(v,S)?v:null,L=Kl(k)?x(k,S):void 0;return w.jsx(jp,{scope:u,value:k,max:S,children:w.jsx(xc.div,{"aria-valuemax":S,"aria-valuemin":0,"aria-valuenow":Kl(k)?k:void 0,"aria-valuetext":L,role:"progressbar","data-state":Ec(k,S),"data-value":k??void 0,"data-max":S,...N,ref:d})})});Sc.displayName=Ji;var kc="ProgressIndicator",Cc=$.forwardRef((s,d)=>{const{__scopeProgress:u,...v}=s,g=Rp(kc,u);return w.jsx(xc.div,{"data-state":Ec(g.value,g.max),"data-value":g.value??void 0,"data-max":g.max,...v,ref:d})});Cc.displayName=kc;function Ip(s,d){return`${Math.round(s/d*100)}%`}function Ec(s,d){return s==null?"indeterminate":s===d?"complete":"loading"}function Kl(s){return typeof s=="number"}function oc(s){return Kl(s)&&!isNaN(s)&&s>0}function ic(s,d){return Kl(s)&&!isNaN(s)&&s<=d&&s>=0}function Tp(s,d){return`Invalid prop \`max\` of value \`${s}\` supplied to \`${d}\`. Only numbers greater than 0 are valid max values. Defaulting to \`${Zi}\`.`}function Lp(s,d){return`Invalid prop \`value\` of value \`${s}\` supplied to \`${d}\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or ${Zi} if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`}var _c=Sc,Mp=Cc;const Hn=$.forwardRef(({className:s,value:d,...u},v)=>w.jsx(_c,{ref:v,className:Mt("relative h-2 w-full overflow-hidden rounded-full bg-zinc-900/20 dark:bg-zinc-50/20",s),...u,children:w.jsx(Mp,{className:"h-full w-full flex-1 bg-zinc-900 transition-all dark:bg-zinc-50",style:{transform:`translateX(-${100-(d||0)}%)`}})}));Hn.displayName=_c.displayName;class Op{constructor(){this._apiKey=null,this._isInitialized=!1}initialize(d){if(!d||typeof d!="string"||d.trim()==="")throw new Error("无效的API密钥");this._apiKey=d,this._isInitialized=!0,console.log("API密钥已安全初始化")}getApiKey(){if(!this._isInitialized)throw new Error("API密钥未初始化");return this._apiKey}isInitialized(){return this._isInitialized}clear(){this._apiKey=null,this._isInitialized=!1,console.log("API密钥已清除")}}const rn=new Op,Ap=`
请为{age_range}岁的自闭症儿童创作一个以"{theme}"为主题的12页绘本故事。

要求：
1. 故事应该简单、清晰，使用具体而非抽象的语言
2. 每页内容控制在100-150字左右
3. 使用简单的句子结构和明确的因果关系
4. 避免使用隐喻、反语或复杂的修辞手法
5. 故事中应包含明确的情感表达和社交互动场景
6. 在故事中随机安排3个交互环节（第4页、第8页和第11页），这些页面需要设计问题
7. 交互问题应鼓励自闭症儿童进行语言表达，不是简单的选择题
8. 每个交互问题都需要配备一个引导提示，用于在儿童30秒内没有回答时提供帮助

输出格式：
{
  "title": "故事标题",
  "pages": [
    {
      "page_number": 1,
      "content": "第1页内容...",
      "is_interactive": false
    },
    {
      "page_number": 4,
      "content": "第4页内容...",
      "is_interactive": true,
      "interactive_question": "问题内容...",
      "guidance_prompt": "引导提示内容..."
    },
    // 其他页面...
  ]
}
`,Dp=`
为自闭症儿童绘本创作一张插图，描述以下场景：

"{scene_description}"

要求：
1. 使用明亮、柔和的色彩
2. 简洁清晰的线条和形状
3. 避免过于复杂或混乱的背景
4. 角色表情要明确、易于识别
5. 图像应具有温暖、友好的氛围
6. 适合{age_range}岁自闭症儿童的视觉感知特点
7. 风格应保持一致，类似儿童插画书
8. 避免使用过多的文字或抽象符号

图像应该能够直观地表达场景内容，帮助自闭症儿童理解故事情节。
`,Fp=`
为{age_range}岁自闭症儿童设计一个关于"{context}"的交互问题。

故事背景：
"{story_context}"

要求：
1. 问题应促进语言表达，而非简单的是/否回答
2. 问题应与故事情节和主题"{theme}"相关
3. 问题应考虑自闭症儿童的认知特点
4. 问题应鼓励分享个人经历或想法
5. 问题应有明确的焦点，避免模糊或抽象
6. 同时设计一个引导提示，用于在儿童30秒内没有回答时提供帮助

输出格式：
{
  "question": "问题内容...",
  "guidance_prompt": "引导提示内容..."
}
`,Up=`
分析自闭症儿童在以下交互问题中的回答表现：

问题1："{question1}"
回答1："{answer1}"

问题2："{question2}"
回答2："{answer2}"

问题3："{question3}"
回答3："{answer3}"

请从以下四个维度进行评估（满分5分）：
1. 语言词汇量：评估词汇丰富度、表达多样性
2. 思维逻辑：评估因果关系理解、逻辑推理能力
3. 社会适应：评估社交规则理解、人际互动意识
4. 情感识别：评估情感表达、共情能力

对于每个维度，请提供具体分析和改进建议。

输出格式：
{
  "scores": {
    "language_vocabulary": 分数,
    "logical_thinking": 分数,
    "social_adaptation": 分数,
    "emotional_recognition": 分数
  },
  "analysis": {
    "language_vocabulary": "分析和建议...",
    "logical_thinking": "分析和建议...",
    "social_adaptation": "分析和建议...",
    "emotional_recognition": "分析和建议..."
  },
  "overall_recommendation": "综合建议..."
}
`;class Vp{constructor(){this.baseUrl="https://api.openai.com/v1",this.modelName="gpt-4o",this.imageModelName="dall-e-3"}getHeaders(){return{"Content-Type":"application/json",Authorization:`Bearer ${rn.getApiKey()}`}}async generateStory(d,u){var v;try{if(!rn.isInitialized())throw new Error("API密钥未初始化");const g=Ap.replace("{age_range}",d).replace("{theme}",u),x=await fetch(`${this.baseUrl}/chat/completions`,{method:"POST",headers:this.getHeaders(),body:JSON.stringify({model:this.modelName,messages:[{role:"system",content:"你是一位专业的儿童绘本作家，擅长为自闭症儿童创作故事。"},{role:"user",content:g}],temperature:.7,max_tokens:2e3})});if(!x.ok){const k=await x.json();throw new Error(`OpenAI API错误: ${((v=k.error)==null?void 0:v.message)||"未知错误"}`)}const S=(await x.json()).choices[0].message.content;try{return JSON.parse(S)}catch(k){return console.error("故事内容解析失败，返回原始文本",k),{raw:S}}}catch(g){throw console.error("生成故事失败:",g),g}}async generateImage(d,u){var v;try{if(!rn.isInitialized())throw new Error("API密钥未初始化");const g=Dp.replace("{scene_description}",d).replace("{age_range}",u),x=await fetch(`${this.baseUrl}/images/generations`,{method:"POST",headers:this.getHeaders(),body:JSON.stringify({model:this.imageModelName,prompt:g,n:1,size:"1024x1024",quality:"standard",response_format:"url"})});if(!x.ok){const S=await x.json();throw new Error(`OpenAI API错误: ${((v=S.error)==null?void 0:v.message)||"未知错误"}`)}return(await x.json()).data[0].url}catch(g){throw console.error("生成图片失败:",g),g}}async generateInteractiveQuestion(d,u,v,g){var x;try{if(!rn.isInitialized())throw new Error("API密钥未初始化");const N=Fp.replace("{age_range}",d).replace("{theme}",u).replace("{story_context}",v).replace("{context}",g),S=await fetch(`${this.baseUrl}/chat/completions`,{method:"POST",headers:this.getHeaders(),body:JSON.stringify({model:this.modelName,messages:[{role:"system",content:"你是一位专业的儿童教育专家，擅长设计适合自闭症儿童的互动问题。"},{role:"user",content:N}],temperature:.7,max_tokens:500})});if(!S.ok){const W=await S.json();throw new Error(`OpenAI API错误: ${((x=W.error)==null?void 0:x.message)||"未知错误"}`)}const L=(await S.json()).choices[0].message.content;try{return JSON.parse(L)}catch(W){return console.error("问题内容解析失败，返回原始文本",W),{question:L,guidance_prompt:"请尝试回答问题，如果不确定，可以描述你的想法。"}}}catch(N){throw console.error("生成交互问题失败:",N),N}}async analyzePerformance(d,u){var v;try{if(!rn.isInitialized())throw new Error("API密钥未初始化");if(d.length!==u.length||d.length===0)throw new Error("问题和回答数量不匹配或为空");let g=Up;for(let k=0;k<d.length;k++)g=g.replace(`{question${k+1}}`,d[k]).replace(`{answer${k+1}}`,u[k]);const x=await fetch(`${this.baseUrl}/chat/completions`,{method:"POST",headers:this.getHeaders(),body:JSON.stringify({model:this.modelName,messages:[{role:"system",content:"你是一位专业的儿童心理学家，擅长评估自闭症儿童的语言和社交能力。"},{role:"user",content:g}],temperature:.3,max_tokens:1e3})});if(!x.ok){const k=await x.json();throw new Error(`OpenAI API错误: ${((v=k.error)==null?void 0:v.message)||"未知错误"}`)}const S=(await x.json()).choices[0].message.content;try{return JSON.parse(S)}catch(k){return console.error("分析内容解析失败，返回原始文本",k),{raw:S}}}catch(g){throw console.error("分析用户表现失败:",g),g}}initializeApiKey(d){rn.initialize(d)}isApiKeyInitialized(){return rn.isInitialized()}clearApiKey(){rn.clear()}}const Ql=new Vp;function $p({className:s=""}){return w.jsx("div",{className:`animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full ${s}`})}function sc({children:s,variant:d="default"}){const u={default:"bg-blue-50 border-blue-200 text-blue-800",destructive:"bg-red-50 border-red-200 text-red-800",warning:"bg-amber-50 border-amber-200 text-amber-800"};return w.jsx("div",{className:`p-4 mb-4 border rounded-md ${u[d]}`,children:s})}function Wp({onStoryGenerated:s,ageRange:d,theme:u}){const[v,g]=$.useState(!1),[x,N]=$.useState(null),[S,k]=$.useState(!1);$.useEffect(()=>{k(Ql.isApiKeyInitialized())},[]);const L=A=>{try{Ql.initializeApiKey(A),k(!0),N(null)}catch(D){N("API密钥设置失败: "+D.message),k(!1)}},W=async()=>{if(!S){N("请先设置API密钥");return}g(!0),N(null);try{const A=await Ql.generateStory(d,u);s(A)}catch(A){N("故事生成失败: "+A.message)}finally{g(!1)}};return w.jsxs("div",{className:"space-y-4",children:[!S&&w.jsx(sc,{variant:"warning",children:w.jsx("p",{children:"请设置OpenAI API密钥以启用故事生成功能"})}),x&&w.jsx(sc,{variant:"destructive",children:w.jsx("p",{children:x})}),w.jsxs("div",{className:"flex gap-2",children:[w.jsx(ot,{onClick:W,disabled:v||!S,children:v?w.jsxs(w.Fragment,{children:[w.jsx($p,{className:"mr-2"}),"生成故事中..."]}):"生成新故事"}),!S&&w.jsx(ot,{variant:"outline",onClick:()=>L(prompt("请输入OpenAI API密钥")),children:"设置API密钥"})]})]})}function uc(s){try{return Ql.initializeApiKey(s),!0}catch(d){return console.error("初始化OpenAI API密钥失败:",d),!1}}function Bp(){const[s,d]=$.useState(0),[u,v]=$.useState([]),[g,x]=$.useState(!0),[N,S]=$.useState(!1),[k,L]=$.useState(null),[W,A]=$.useState(!1),[D,ne]=$.useState(!1),[q,B]=$.useState(""),[I,Y]=$.useState(""),oe=wt.pages.length,Ce=(s+1)/oe*100,ue=wt.pages[s];$.useEffect(()=>{const b=localStorage.getItem("openai_api_key");if(b)try{const fe=uc(b);A(fe),fe&&B(b)}catch(fe){console.error("初始化API密钥失败:",fe)}},[]);const Ie=()=>{if(!q.trim()){Y("请输入有效的API密钥");return}try{uc(q)?(A(!0),Y(""),ne(!1),localStorage.setItem("openai_api_key",q)):Y("API密钥设置失败")}catch(b){console.error("设置API密钥失败:",b),Y("API密钥设置失败: "+b.message)}},Ee=()=>{"speechSynthesis"in window&&window.speechSynthesis.cancel(),s<oe-1?(d(s+1),window.scrollTo(0,0)):ye()},ze=()=>{"speechSynthesis"in window&&window.speechSynthesis.cancel(),s>0&&(d(s-1),window.scrollTo(0,0))},Le=(b,fe)=>{const ce=u.findIndex(ee=>ee.pageId===b);if(ce>=0){const ee=[...u];ee[ce]={pageId:b,response:fe},v(ee)}else v([...u,{pageId:b,response:fe}])},re=()=>{x(!1)},ye=()=>{const b=wt.pages.filter(C=>C.isInteractive),fe=u.length;let ce=0,ee=0,P=0,F=0;u.forEach(C=>{const G=C.response.toLowerCase(),Z=G.split(/\s+/),le=new Set(Z);ce+=Math.min(Z.length/5,5)+Math.min(le.size/3,5);const me=["因为","所以","如果","但是","然后","接着","首先","其次","最后"].filter(Ot=>G.includes(Ot)).length;ee+=Math.min(me*2,5);const ge=["朋友","一起","帮助","分享","谢谢","请","对不起","合作","玩"].filter(Ot=>G.includes(Ot)).length;P+=Math.min(ge*2,5);const yn=["高兴","难过","害怕","生气","担心","开心","喜欢","爱","紧张","兴奋"].filter(Ot=>G.includes(Ot)).length;F+=Math.min(yn*2,5)});const R=C=>fe===0?0:Math.min(Math.round(C/fe*10)/10,5),p={completedInteractions:fe,totalInteractions:b.length,scores:{languageVocabulary:R(ce),logicalThinking:R(ee),socialAdaptation:R(P),emotionalRecognition:R(F)},recommendations:he(R(ce),R(ee),R(P),R(F))};L(p),S(!0)},he=(b,fe,ce,ee)=>{const P={languageVocabulary:"",logicalThinking:"",socialAdaptation:"",emotionalRecognition:"",overall:""};b<2?P.languageVocabulary="词汇量较为有限，表达方式简单。建议通过更多的阅读和对话活动，扩展孩子的词汇库。":b<4?P.languageVocabulary="具备基本的词汇表达能力，能够使用简单句型进行交流。建议鼓励使用更丰富的形容词和动词。":P.languageVocabulary="词汇量丰富，能够使用多样化的词汇进行表达。建议继续通过阅读拓展专业领域词汇。",fe<2?P.logicalThinking="逻辑表达能力需要加强，因果关系理解有限。建议通过简单的推理游戏培养逻辑思维能力。":fe<4?P.logicalThinking="能够理解基本的因果关系，表达有一定的逻辑性。建议通过更复杂的问题解决活动提升逻辑思维。":P.logicalThinking="逻辑思维能力较强，能够清晰地表达因果关系和推理过程。建议尝试更复杂的逻辑推理活动。",ce<2?P.socialAdaptation="社交互动意识较弱，对社交规则理解有限。建议通过角色扮演游戏培养基本社交技能。":ce<4?P.socialAdaptation="具备基本的社交意识，能够理解简单的社交规则。建议增加小组活动，提升社交互动能力。":P.socialAdaptation="社交适应能力良好，能够理解并应用社交规则。建议参与更多团体活动，进一步提升社交能力。",ee<2?P.emotionalRecognition="情感识别和表达能力有限，难以准确表达自身情感。建议通过情绪卡片游戏增强情感识别能力。":ee<4?P.emotionalRecognition="能够识别基本情绪，有一定的情感表达能力。建议通过讨论故事人物情感，提升情感理解深度。":P.emotionalRecognition="情感识别能力较强，能够准确表达和理解多种情绪。建议探索更复杂的情感状态和共情能力培养。";const F=(b+fe+ce+ee)/4;return F<2?P.overall="建议增加日常交流互动，使用简单明确的语言，配合视觉提示辅助理解。可以通过结构化的社交故事和游戏，逐步提升语言表达和社交能力。":F<4?P.overall="孩子具备基本的交流能力，建议通过更多的小组活动和角色扮演，提升社交互动质量。同时，可以引导孩子表达更复杂的情感和想法，培养共情能力。":P.overall="孩子在语言交流方面表现良好，建议提供更具挑战性的社交情境，如解决冲突、协商合作等，进一步提升高阶社交能力和情感表达深度。",P},X=()=>{d(0),v([]),S(!1),x(!0)},Ae=async b=>{console.log("生成的新故事:",b),d(0),v([]),S(!1),x(!1)};return g?w.jsx("div",{className:"flex flex-col items-center justify-center min-h-screen p-4 bg-amber-50",children:w.jsxs("div",{className:"max-w-2xl w-full bg-white rounded-lg shadow-xl p-8",children:[w.jsx("h1",{className:"text-3xl font-bold text-center mb-6",children:wt.title}),w.jsxs("div",{className:"mb-6",children:[w.jsxs("p",{className:"text-lg mb-4",children:["这是一个为",wt.ageGroup,'自闭症儿童设计的交互式绘本，主题是"',wt.theme,'"。']}),w.jsx("p",{className:"mb-4",children:"在这个故事中，你将跟随小熊波波的冒险，学习友谊的重要性。故事中有3个交互环节，你需要回答问题，帮助波波做出选择。"}),w.jsx("p",{className:"mb-4",children:"完成所有交互后，系统会生成一份评估报告，分析你在语言词汇量、思维逻辑、社会适应和情感识别四个维度的表现。"}),w.jsx("p",{className:"mb-4 font-semibold text-blue-600",children:"新功能：本绘本支持语音朗读和语音输入，让体验更加便捷！"}),!W&&w.jsxs("div",{className:"mt-6 mb-4",children:[w.jsx(ot,{variant:"outline",onClick:()=>ne(!D),children:D?"隐藏API密钥输入":"设置OpenAI API密钥"}),D&&w.jsxs("div",{className:"mt-4 p-4 border rounded-md",children:[w.jsx("p",{className:"mb-2",children:"设置OpenAI API密钥以启用AI故事和图片生成功能"}),w.jsxs("div",{className:"flex gap-2",children:[w.jsx("input",{type:"password",value:q,onChange:b=>B(b.target.value),placeholder:"输入OpenAI API密钥",className:"flex-1 p-2 border rounded"}),w.jsx(ot,{onClick:Ie,children:"设置"})]}),I&&w.jsx("p",{className:"mt-2 text-red-500",children:I})]})]}),W&&w.jsxs("div",{className:"mt-6 mb-4",children:[w.jsx("p",{className:"text-green-600 mb-2",children:"✓ OpenAI API密钥已设置，AI生成功能已启用"}),w.jsx(Wp,{onStoryGenerated:Ae,ageRange:wt.ageGroup,theme:wt.theme})]})]}),w.jsx("div",{className:"flex justify-center",children:w.jsx(ot,{onClick:re,size:"lg",children:"开始阅读"})})]})}):N?w.jsx("div",{className:"flex flex-col items-center justify-center min-h-screen p-4 bg-amber-50",children:w.jsxs("div",{className:"max-w-3xl w-full bg-white rounded-lg shadow-xl p-8",children:[w.jsx("h1",{className:"text-3xl font-bold text-center mb-6",children:"自闭症儿童语言交互能力评估报告"}),w.jsxs("div",{className:"mb-6",children:[w.jsx("h2",{className:"text-xl font-semibold mb-2",children:"基本信息"}),w.jsxs("p",{children:["年龄段：",wt.ageGroup]}),w.jsxs("p",{children:["绘本主题：",wt.theme]}),w.jsxs("p",{children:["完成交互环节数量：",k.completedInteractions,"/",k.totalInteractions]})]}),w.jsxs("div",{className:"mb-6",children:[w.jsx("h2",{className:"text-xl font-semibold mb-4",children:"能力维度评估（满分5分）"}),w.jsxs("div",{className:"space-y-4",children:[w.jsxs("div",{children:[w.jsxs("div",{className:"flex justify-between mb-1",children:[w.jsxs("span",{children:["语言词汇量：",k.scores.languageVocabulary,"分"]}),w.jsxs("span",{children:[k.scores.languageVocabulary,"/5"]})]}),w.jsx(Hn,{value:k.scores.languageVocabulary*20,className:"h-2"})]}),w.jsxs("div",{children:[w.jsxs("div",{className:"flex justify-between mb-1",children:[w.jsxs("span",{children:["思维逻辑：",k.scores.logicalThinking,"分"]}),w.jsxs("span",{children:[k.scores.logicalThinking,"/5"]})]}),w.jsx(Hn,{value:k.scores.logicalThinking*20,className:"h-2"})]}),w.jsxs("div",{children:[w.jsxs("div",{className:"flex justify-between mb-1",children:[w.jsxs("span",{children:["社会适应：",k.scores.socialAdaptation,"分"]}),w.jsxs("span",{children:[k.scores.socialAdaptation,"/5"]})]}),w.jsx(Hn,{value:k.scores.socialAdaptation*20,className:"h-2"})]}),w.jsxs("div",{children:[w.jsxs("div",{className:"flex justify-between mb-1",children:[w.jsxs("span",{children:["情感识别：",k.scores.emotionalRecognition,"分"]}),w.jsxs("span",{children:[k.scores.emotionalRecognition,"/5"]})]}),w.jsx(Hn,{value:k.scores.emotionalRecognition*20,className:"h-2"})]})]})]}),w.jsxs("div",{className:"mb-6",children:[w.jsx("h2",{className:"text-xl font-semibold mb-2",children:"详细分析"}),w.jsxs("div",{className:"space-y-4",children:[w.jsxs("div",{children:[w.jsx("h3",{className:"font-medium",children:"语言词汇量"}),w.jsx("p",{children:k.recommendations.languageVocabulary})]}),w.jsxs("div",{children:[w.jsx("h3",{className:"font-medium",children:"思维逻辑"}),w.jsx("p",{children:k.recommendations.logicalThinking})]}),w.jsxs("div",{children:[w.jsx("h3",{className:"font-medium",children:"社会适应"}),w.jsx("p",{children:k.recommendations.socialAdaptation})]}),w.jsxs("div",{children:[w.jsx("h3",{className:"font-medium",children:"情感识别"}),w.jsx("p",{children:k.recommendations.emotionalRecognition})]})]})]}),w.jsxs("div",{className:"mb-6",children:[w.jsx("h2",{className:"text-xl font-semibold mb-2",children:"总结建议"}),w.jsx("p",{children:k.recommendations.overall})]}),w.jsx("div",{className:"flex justify-center",children:w.jsx(ot,{onClick:X,size:"lg",children:"重新开始"})})]})}):w.jsx("div",{className:"min-h-screen bg-amber-50 py-8",children:w.jsxs("div",{className:"max-w-4xl mx-auto px-4",children:[w.jsxs("header",{className:"mb-6",children:[w.jsx("h1",{className:"text-3xl font-bold text-center",children:wt.title}),w.jsxs("div",{className:"mt-4",children:[w.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[w.jsxs("span",{children:["第 ",s+1," 页，共 ",oe," 页"]}),w.jsxs("span",{children:[Math.round(Ce),"%"]})]}),w.jsx(Hn,{value:Ce,className:"h-2"})]})]}),w.jsx(Ep,{page:ue,onNext:Ee,onResponseSubmit:Le,userResponses:u}),w.jsxs("div",{className:"flex justify-between mt-6",children:[w.jsx(ot,{variant:"outline",onClick:ze,disabled:s===0,children:"上一页"}),!ue.isInteractive&&w.jsx(ot,{onClick:Ee,disabled:s===oe-1,children:s===oe-1?"完成阅读":"下一页"})]})]})})}function Hp(){return w.jsx("div",{className:"App",children:w.jsx(Bp,{})})}Rf.createRoot(document.getElementById("root")).render(w.jsx($.StrictMode,{children:w.jsx(Hp,{})}));
