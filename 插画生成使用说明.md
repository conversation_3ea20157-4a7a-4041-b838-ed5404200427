# 《小熊波波友谊冒险》插画生成使用说明

## 概述

本工具使用OpenAI的DALL-E 3模型为绘本故事《小熊波波友谊冒险》自动生成一套风格一致的插画。工具会为所有非交互页面（第1、2、3、5、6、7、9、10、12页）生成插画。

## 功能特点

- ✨ **风格一致性**: 所有插画保持统一的儿童绘本风格
- 🎨 **角色一致性**: 主要角色（波波、莉莉等）在所有插画中保持一致的外观
- 🤖 **自动化流程**: 一键生成、下载并保存所有插画
- 📊 **详细报告**: 生成完整的处理报告和统计信息
- 🔄 **错误恢复**: 支持重新生成失败的页面
- 💾 **备份机制**: 自动备份现有图片

## 前置要求

1. **Node.js环境**: 确保已安装Node.js (版本14+)
2. **OpenAI API密钥**: 需要有效的OpenAI API密钥
3. **网络连接**: 需要稳定的网络连接访问OpenAI API

## 安装和设置

### 1. 设置API密钥

有两种方式设置OpenAI API密钥：

**方式一：环境变量（推荐）**
```bash
export OPENAI_API_KEY="your-openai-api-key-here"
```

**方式二：直接在代码中设置**
在 `runIllustrationGeneration.js` 文件中的 `getApiKey()` 函数中直接返回你的API密钥。

### 2. 安装依赖

```bash
npm install
```

## 使用方法

### 测试功能

在生成完整插画集之前，建议先运行测试：

```bash
npm run test-illustration
```

这会：
- 验证API密钥是否有效
- 生成第1页的测试插画
- 测试下载和保存功能

### 生成完整插画集

```bash
npm run generate-illustrations
```

这会：
1. 为所有非交互页面生成插画
2. 自动下载并保存到 `public` 目录
3. 生成详细的处理报告
4. 更新故事数据文件

## 生成的文件

### 插画文件
- `public/page1.png` - 第1页插画
- `public/page2.png` - 第2页插画
- `public/page3.png` - 第3页插画
- `public/page5.png` - 第5页插画
- `public/page6.png` - 第6页插画
- `public/page7.png` - 第7页插画
- `public/page9.png` - 第9页插画
- `public/page10.png` - 第10页插画
- `public/page12.png` - 第12页插画

### 报告文件
- `illustration_generation_report.json` - 详细的生成报告
- `updated_storyData.json` - 更新后的故事数据

### 备份文件
- `backup/` 目录 - 原有图片的备份

## 风格设定

### 角色设定
- **小熊波波**: 可爱的棕色小熊，圆脸，大眼睛，红色背心
- **小兔莉莉**: 灰白色兔子，长耳朵，粉红鼻子，蓝色裙子
- **猫头鹰**: 智慧的棕色猫头鹰，戴小眼镜
- **松鼠兄弟**: 红棕色松鼠，蓬松尾巴
- **乌龟**: 绿色乌龟，友善表情

### 艺术风格
- 温暖友好的儿童插画风格
- 水彩画质感，轮廓清晰但柔和
- 柔和明亮的色彩：棕色、绿色、蓝色、黄色
- 简洁清晰的构图
- 温馨舒适的氛围

## 故障排除

### 常见问题

**1. API密钥错误**
```
错误: 未提供OpenAI API密钥
```
解决方案：确保正确设置了OPENAI_API_KEY环境变量

**2. 网络连接问题**
```
错误: 下载超时
```
解决方案：检查网络连接，重新运行生成命令

**3. 磁盘空间不足**
```
错误: ENOSPC: no space left on device
```
解决方案：清理磁盘空间，每张图片约1-3MB

**4. API配额限制**
```
错误: Rate limit exceeded
```
解决方案：等待一段时间后重新运行，或升级API计划

### 重新生成特定页面

如果某个页面生成失败，可以手动重新生成：

```javascript
import { regeneratePageIllustration } from './generateStoryIllustrations.js';

// 重新生成第1页
const result = await regeneratePageIllustration(1, "your-api-key");
```

## 成本估算

- DALL-E 3标准质量：约$0.04/张图片
- 总计9张图片：约$0.36
- 建议预留一些额度用于重新生成

## 注意事项

1. **API限制**: DALL-E 3有速率限制，脚本已添加延迟
2. **图片质量**: 生成的图片为1024x1024像素，适合数字显示
3. **风格一致性**: 虽然使用了详细的提示词，但AI生成的图片可能存在细微差异
4. **备份重要**: 生成前会自动备份现有图片
5. **网络稳定**: 确保网络连接稳定，避免下载中断

## 技术架构

### 核心模块
- `styleConsistencyManager.js` - 风格一致性管理
- `imageProcessor.js` - 图片处理工具
- `generateStoryIllustrations.js` - 主要生成逻辑
- `promptTemplates.js` - 提示词模板

### 工作流程
1. 读取故事数据
2. 为每页构建一致性提示词
3. 调用DALL-E 3 API生成图片
4. 下载并保存图片到本地
5. 验证风格一致性
6. 生成处理报告

## 支持

如果遇到问题，请检查：
1. API密钥是否有效
2. 网络连接是否稳定
3. 磁盘空间是否充足
4. Node.js版本是否兼容

生成的报告文件包含详细的错误信息，有助于问题诊断。
